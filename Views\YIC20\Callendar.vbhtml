@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    ViewData("Title") = "行事曆"
    'Dim id As String = "_" + Guid.NewGuid.ToString
End Code

<style>
    body {
        font-family: Arial, sans-serif;
        background-color: #f4f6f8;
        margin: 0;
        padding: 0;
    }

    #calendar {
        /* 參考 Google 日曆 - 響應式設計，充分利用瀏覽器空間 */
        max-width: none;
        width: calc(100vw - 40px); /* 瀏覽器寬度減去左右各 20px */
        margin: 10px 20px;
        background: white !important;
        padding: 15px;
        border-radius: 8px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
        min-width: 800px; /* 最小寬度確保可用性 */
    }

    /* 響應式設計 - 大螢幕優化 */
    @@media (min-width: 1400px) {
        #calendar {
            width: calc(100vw - 60px);
            margin: 15px 30px;
            padding: 20px;
        }
    }

    /* 響應式設計 - 中等螢幕 */
    @@media (max-width: 1200px) {
        #calendar {
            width: calc(100vw - 30px);
            margin: 10px 15px;
            padding: 12px;
        }
    }

    /* 響應式設計 - 小螢幕 */
    @@media (max-width: 768px) {
        #calendar {
            width: calc(100vw - 20px);
            margin: 5px 10px;
            padding: 10px;
            min-width: 320px;
        }
    }

    /* 修正 FullCalendar 視圖背景色問題 */
    .fc-view-container,
    .fc-view,
    .fc-agenda-view .fc-time-grid-container,
    .fc-agenda-view .fc-time-grid,
    .fc-agenda-view .fc-day-grid-container,
    .fc-agenda-view .fc-day-grid {
        background-color: white !important;
        background: white !important;
    }

    /* 確保時間槽正常顯示 */
    .fc-time-grid .fc-slats table,
    .fc-time-grid .fc-slats tr,
    .fc-time-grid .fc-slats td {
        background: white !important;
        background-color: white !important;
    }

    /* 修正表格背景 */
    .fc table,
    .fc table thead,
    .fc table tbody,
    .fc table tr,
    .fc table td,
    .fc table th {
        background: white !important;
        background-color: white !important;
    }

    /* 修正任何可能的粉紅色背景 */
    .fc-widget-content,
    .fc-widget-header {
        background-color: white !important;
        background: white !important;
    }

    /* 改善跨天事件顯示 - 參考 Google 日曆，增大字體 */
    .fc-event {
        border-radius: 4px !important;
        border: 1px solid rgba(0,0,0,0.1) !important;
        font-size: 14px !important;
        padding: 4px 6px !important;
        margin: 2px 0 !important;
        opacity: 0.9 !important;
        line-height: 1.3 !important;
    }

    /* 週視圖中的跨天事件樣式 - 增大尺寸 */
    .fc-agenda-view .fc-event {
        border-radius: 4px !important;
        min-height: 24px !important;
        line-height: 20px !important;
        font-size: 14px !important;
        padding: 4px 6px !important;
    }

    /* 確保跨天事件在週視圖中連續顯示 */
    .fc-agenda-view .fc-event.fc-start {
        border-top-left-radius: 4px !important;
        border-bottom-left-radius: 4px !important;
        border-top-right-radius: 0 !important;
        border-bottom-right-radius: 0 !important;
        margin-right: 0 !important;
    }

    .fc-agenda-view .fc-event.fc-end {
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-top-right-radius: 4px !important;
        border-bottom-right-radius: 4px !important;
        margin-left: 0 !important;
    }

    .fc-agenda-view .fc-event.fc-middle {
        border-radius: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* 改善事件文字顯示 */
    .fc-event .fc-title,
    .fc-event .fc-time {
        color: white !important;
        font-weight: 500 !important;
        text-shadow: 0 1px 1px rgba(0,0,0,0.3) !important;
    }

    /* 月視圖中的事件樣式 - 增大字體和間距 */
    .fc-month-view .fc-event {
        border-radius: 4px !important;
        margin: 2px 1px !important;
        padding: 3px 5px !important;
        font-size: 13px !important;
        min-height: 20px !important;
        line-height: 1.2 !important;
    }

    /* 跨天事件特殊樣式 */
    .multi-day-event {
        box-shadow: 0 1px 3px rgba(0,0,0,0.2) !important;
        font-weight: 500 !important;
    }

    /* 改善週視圖中的時間網格 */
    .fc-agenda-view .fc-time-grid .fc-slats td {
        border-top: 1px solid #e0e0e0 !important;
    }

    /* 改善事件在時間槽中的對齊 */
    .fc-agenda-view .fc-time-grid-event {
        margin-bottom: 1px !important;
        margin-top: 1px !important;
    }

    /* 確保跨天事件在週視圖中有足夠的可見性 */
    .fc-agenda-view .fc-time-grid-event.multi-day-event {
        min-height: 22px !important;
        border-width: 2px !important;
        font-size: 11px !important;
        line-height: 18px !important;
    }

    /* 改善全天事件區域 - 增大高度和字體 */
    .fc-agenda-view .fc-day-grid .fc-event {
        margin: 3px 2px !important;
        padding: 4px 6px !important;
        min-height: 22px !important;
        font-size: 14px !important;
    }

    /* 增加月視圖的行高 */
    .fc-month-view .fc-week {
        min-height: 120px !important;
    }

    .fc-month-view td {
        min-height: 120px !important;
        vertical-align: top !important;
    }

    /* 增加週視圖的時間槽高度 - 確保24小時可滾動顯示 */
    .fc-agenda-view .fc-time-grid .fc-slats td {
        height: 60px !important; /* 增加到60px確保24小時有足夠空間 */
    }

    /* 確保時間軸容器可以滾動 */
    .fc-agenda-view .fc-time-grid-container {
        overflow-y: auto !important;
        max-height: 600px !important; /* 限制最大高度，強制滾動 */
    }

    /* 確保時間軸有足夠的總高度 */
    .fc-agenda-view .fc-time-grid .fc-slats {
        min-height: 1440px !important; /* 24小時 × 60px = 1440px */
    }

    /* 改善日視圖的顯示 */
    .fc-agenda-view .fc-time-grid-event {
        margin-bottom: 2px !important;
        margin-top: 2px !important;
    }

    /* 參考 Google 日曆 - 改善大螢幕顯示和字體大小 */
    .fc-toolbar {
        margin-bottom: 20px !important;
        padding: 0 5px !important;
    }

    .fc-toolbar h2 {
        font-size: 28px !important;
        font-weight: 400 !important;
        color: #3c4043 !important;
    }

    .fc-button-group .fc-button {
        padding: 10px 18px !important;
        font-size: 16px !important;
        border-radius: 4px !important;
        margin: 0 2px !important;
    }

    /* 改善月視圖的日期格子大小和字體 */
    .fc-month-view .fc-day-number {
        font-size: 18px !important;
        padding: 12px !important;
        font-weight: 500 !important;
    }

    .fc-month-view .fc-day-top {
        padding: 12px 14px !important;
    }

    /* 改善週視圖的時間標籤 */
    .fc-agenda-view .fc-axis {
        width: 70px !important;
        font-size: 14px !important;
    }

    /* 改善週視圖的日期標題 */
    .fc-agenda-view .fc-day-header {
        font-size: 16px !important;
        padding: 12px 8px !important;
        font-weight: 500 !important;
    }

    /* 大螢幕優化 - 更大的字體和更好的間距 */
    @@media (min-width: 1400px) {
        .fc-toolbar h2 {
            font-size: 32px !important;
        }

        .fc-button-group .fc-button {
            padding: 12px 20px !important;
            font-size: 18px !important;
        }

        .fc-month-view .fc-day-number {
            font-size: 20px !important;
            padding: 14px !important;
        }

        .fc-month-view .fc-day-top {
            padding: 14px 16px !important;
        }

        .fc-event {
            font-size: 15px !important;
            padding: 5px 7px !important;
            min-height: 26px !important;
        }

        .fc-agenda-view .fc-event {
            font-size: 15px !important;
            min-height: 28px !important;
            padding: 5px 8px !important;
        }

        .fc-agenda-view .fc-day-header {
            font-size: 18px !important;
            padding: 14px 10px !important;
        }

        .fc-agenda-view .fc-axis {
            font-size: 16px !important;
            width: 80px !important;
        }
    }
</style>

<div id="calendar"></div>


<script type="text/javascript">
    $(document).ready(function() {
        console.log('初始化舊版 FullCalendar...');
        
        // 強制清除任何現有的行事曆實例
        if ($('#calendar').hasClass('fc')) {
            $('#calendar').fullCalendar('destroy');
        }
        
        // 等待 DOM 完全載入後再初始化
        setTimeout(function() {
            // 使用舊版 FullCalendar jQuery 語法
            $('#calendar').fullCalendar({
                header: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'month,agendaWeek,agendaDay,listMonth'
                },
                defaultView: 'month',
                defaultDate: '2025-08-15', // 設定預設日期為有事件的日期
                navLinks: true,
                editable: false,
                selectable: true,

                // 參考 Google 日曆 - 可滾動的24小時時間軸設計
                height: 'auto',
                aspectRatio: window.innerWidth > 1400 ? 1.8 : (window.innerWidth > 1200 ? 1.6 : 1.4),
                contentHeight: Math.max(600, Math.min(700, window.innerHeight - 200)),

                // 確保在不同螢幕尺寸下都有良好的顯示效果
                windowResize: function(view) {
                    // 動態調整高度比例 - 可滾動的24小時設計
                    var newAspectRatio = window.innerWidth > 1400 ? 1.8 : (window.innerWidth > 1200 ? 1.6 : 1.4);
                    var newContentHeight = Math.max(600, Math.min(700, window.innerHeight - 200));

                    $('#calendar').fullCalendar('option', 'aspectRatio', newAspectRatio);
                    $('#calendar').fullCalendar('option', 'contentHeight', newContentHeight);
                },

                // 改善跨天事件顯示 - 參考 Google 日曆，顯示完整24小時
                allDaySlot: true,
                allDayText: 'all-day',
                slotDuration: '01:00:00',
                slotLabelInterval: '01:00:00',
                slotLabelFormat: 'ha',
                timeFormat: 'h:mma',
                displayEventTime: true,
                displayEventEnd: true,

                // 設定完整的24小時時間範圍
                minTime: '00:00:00',
                maxTime: '24:00:00',
                scrollTime: '08:00:00',

                // 確保跨天事件正確顯示
                eventOverlap: true,
                slotEventOverlap: true,

                // 改善事件渲染
                eventRender: function(event, element, view) {
                    // 為跨天事件添加特殊樣式
                    if (event.start && event.end) {
                        var startDate = moment(event.start);
                        var endDate = moment(event.end);
                        var isMultiDay = !startDate.isSame(endDate, 'day');

                        if (isMultiDay && (view.name === 'agendaWeek' || view.name === 'agendaDay')) {
                            // 為跨天事件添加特殊類別
                            element.addClass('multi-day-event');

                            // 改善跨天事件的顯示
                            if (view.name === 'agendaWeek') {
                                // 確保事件有足夠的高度和可見性
                                element.css({
                                    'min-height': '20px',
                                    'z-index': '100',
                                    'position': 'relative'
                                });
                            }
                        }
                    }

                    // 確保事件顏色正確顯示
                    if (event.color) {
                        element.css({
                            'background-color': event.color,
                            'border-color': event.color
                        });
                    }

                    return element;
                },
                // 確保視圖正確渲染
                viewRender: function(view, element) {
                    console.log('視圖渲染:', view.name);
                    // 強制設定背景色
                    $('.fc-view-container, .fc-view').css({
                        'background-color': 'white',
                        'background': 'white'
                    });

                    // 智能日期導航：當切換到日視圖時，自動導航到最近的有事件日期
                    if (view.name === 'agendaDay') {
                        var events = $('#calendar').fullCalendar('clientEvents');
                        var currentDate = view.start;
                        var hasEventsOnCurrentDate = events.some(function(event) {
                            return event.start && event.start.isSame(currentDate, 'day');
                        });

                        if (!hasEventsOnCurrentDate && events.length > 0) {
                            // 找到最近的有事件的日期
                            var nearestEventDate = null;
                            var minDiff = Infinity;

                            events.forEach(function(event) {
                                if (event.start) {
                                    var diff = Math.abs(event.start.diff(currentDate, 'days'));
                                    if (diff < minDiff) {
                                        minDiff = diff;
                                        nearestEventDate = event.start;
                                    }
                                }
                            });

                            if (nearestEventDate && !nearestEventDate.isSame(currentDate, 'day')) {
                                console.log('自動導航到有事件的日期:', nearestEventDate.format('YYYY-MM-DD'));
                                setTimeout(function() {
                                    $('#calendar').fullCalendar('gotoDate', nearestEventDate);
                                }, 100);
                            }
                        }
                    }
                },
                // 載入真實的行事曆數據
                events: function(start, end, timezone, callback) {
                    console.log('FullCalendar 正在請求事件數據...');
                    // 呼叫 API 取得行事曆事件
                    $.ajax({
                        url: '@Url.Action("GetCalendarEvents", "YIC20")',
                        type: 'POST',
                        dataType: 'json',
                        timeout: 10000, // 10秒超時
                        success: function(response) {
                            console.log('API 回應:', response);
                            if (response.OK) {
                                console.log('成功載入', response.obj.length, '個事件');
                                // 轉換數據格式以符合舊版 FullCalendar
                                var convertedEvents = response.obj.map(function(event) {
                                    var convertedEvent = {
                                        title: event.title,
                                        start: event.start.replace(/\//g, '-'), // 修正日期格式
                                        color: event.backgroundColor || event.color, // 使用正確的顏色屬性
                                        core: event.extendedProps ? event.extendedProps.core : event.core,
                                        resName: event.extendedProps ? event.extendedProps.resName : event.resName,
                                        borrowerName: event.extendedProps ? event.extendedProps.borrowerName : event.borrowerName,
                                        startTime: event.extendedProps ? event.extendedProps.startTime : event.startTime,
                                        stopTime: event.extendedProps ? event.extendedProps.stopTime : event.stopTime,
                                        // 新增：傳遞聚合事件的重要屬性
                                        eventType: event.eventType,
                                        equipmentCount: event.equipmentCount,
                                        date: event.date
                                    };

                                    // 處理全天事件（核心聚合事件）
                                    if (event.allDay) {
                                        convertedEvent.allDay = true;
                                        // 全天事件不需要 end 時間
                                    } else if (event.end) {
                                        convertedEvent.end = event.end.replace(/\//g, '-'); // 修正日期格式
                                    }

                                    return convertedEvent;
                                });
                                console.log('轉換後的事件:', convertedEvents);
                                callback(convertedEvents);
                            } else {
                                console.error('載入行事曆事件失敗:', response.MSG);
                                callback([]);
                            }
                        },
                        error: function(xhr, status, error) {
                            console.error('AJAX 請求失敗:', error, xhr.responseText);
                            // 提供備用的測試數據
                            callback([{
                                title: '測試事件',
                                start: '2025-08-17',
                                color: '#ff6b6b'
                            }]);
                        }
                    });
                },
                // 事件點擊處理
                eventClick: function(calEvent, jsEvent, view) {
                    // 檢查是否為核心聚合事件
                    if (calEvent.eventType === 'coreAggregate') {
                        // 點擊核心聚合事件時，顯示該核心當天的詳細設備資訊
                        showCoreDetailModal(calEvent.core, calEvent.date);
                    } else {
                        // 原有的詳細事件點擊處理
                        var content = '核心: ' + (calEvent.core || '') + '\n' +
                                     '儀器: ' + (calEvent.resName || '') + '\n' +
                                     '使用者: ' + (calEvent.borrowerName || '') + '\n' +
                                     '時間: ' + (calEvent.startTime || '') + ' - ' + (calEvent.stopTime || '');
                        alert(content);
                    }
                }
            });
            
            console.log('FullCalendar 初始化完成');
        }, 100);
    });

    // 顯示核心詳細資訊的彈出視窗
    function showCoreDetailModal(coreType, eventDate) {
        console.log('顯示核心詳細資訊:', coreType, eventDate);
        
        // 呼叫 API 取得該核心當天的詳細事件
        $.ajax({
            url: '@Url.Action("GetCoreDetailEvents", "YIC20")',
            type: 'POST',
            data: {
                coreType: coreType,
                eventDate: eventDate.replace(/\//g, '-')
            },
            dataType: 'json',
            timeout: 10000,
            success: function(response) {
                console.log('核心詳細事件 API 回應:', response);
                if (response.OK && response.obj && response.obj.length > 0) {
                    displayCoreDetailModal(coreType, eventDate, response.obj);
                } else {
                    alert('該核心當天沒有設備租借資訊');
                }
            },
            error: function(xhr, status, error) {
                console.error('取得核心詳細事件失敗:', error, xhr.responseText);
                alert('取得詳細資訊失敗，請稍後再試');
            }
        });
    }

    // 顯示核心詳細資訊彈出視窗
    function displayCoreDetailModal(coreType, eventDate, events) {
        var modalContent = '<div style="background: white; padding: 20px; border-radius: 8px; box-shadow: 0 4px 20px rgba(0,0,0,0.15); max-width: 600px; max-height: 70vh; overflow-y: auto;">';
        modalContent += '<h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #eee; padding-bottom: 10px;">';
        modalContent += coreType + ' - ' + eventDate + ' 設備租借詳情</h3>';
        
        modalContent += '<div style="margin: 15px 0;">';
        modalContent += '<strong>共有 ' + events.length + ' 台設備被租借</strong>';
        modalContent += '</div>';
        
        events.forEach(function(event, index) {
            modalContent += '<div style="margin: 10px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid ' + event.color + '; border-radius: 4px;">';
            modalContent += '<div style="font-weight: bold; color: #333; margin-bottom: 5px;">' + (index + 1) + '. ' + event.resName + '</div>';
            modalContent += '<div style="color: #666; font-size: 14px;">使用者：' + event.borrowerName + '</div>';
            modalContent += '<div style="color: #666; font-size: 14px;">時間：' + event.startTime + ' - ' + event.stopTime + '</div>';
            modalContent += '</div>';
        });
        
        modalContent += '<div style="text-align: center; margin-top: 20px;">';
        modalContent += '<button onclick="closeCoreDetailModal()" style="background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer;">關閉</button>';
        modalContent += '</div>';
        modalContent += '</div>';
        
        // 建立遮罩層
        var overlay = '<div id="coreDetailOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; justify-content: center; align-items: center;" onclick="closeCoreDetailModal()">';
        overlay += modalContent;
        overlay += '</div>';
        
        // 添加到頁面
        $('body').append(overlay);
    }

    // 關閉核心詳細資訊彈出視窗
    function closeCoreDetailModal() {
        $('#coreDetailOverlay').remove();
    }
</script>






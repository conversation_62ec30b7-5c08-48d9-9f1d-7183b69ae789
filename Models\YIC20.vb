﻿Imports Newtonsoft.Json
Public Enum SelModeEnum As Integer
    Sing = 0
    Multi = 1
End Enum


#Region "開放時間、收費標準、設備管理"

Public Class YXA20Model
    Public Property objid As String
    Public Property SemiYear As String
    Public Property Semistry As String
    '權責單位
    Public Property DutyOrgID As String
    '權責單位名稱
    Public Property DutyOrgName As String
    '資源編號
    Public Property ResID As String
    '資源名稱
    Public Property ResName As String
    '資源類別(小分類)  例如:B0301:一般,視聽,電腦實習...
    Public Property SubClassEx As String
    '週別
    Public Property HourType As String

    Public Property HourTypeStr As String
    '週次
    Public Property Week As String
    '星期
    Public Property DayOfWeek As String
    '起始日期
    Public Property StartDate As String
    '起始時間
    Public Property StartTime As String
    '結束日期
    Public Property StopDate As String
    '結束時間
    Public Property StopTime As String
    '開放時間(顯示在畫面上)
    Public Property OPDTStr As String
    '租借對象
    Public Property BorrowerID As String
    Public Property Borrower As String
    '開放時段(YYXX.....)
    Public Property Hour As String
    '容納人數
    Public Property Contains As String
End Class



Public Class YXA20QModel
    '物件編號Key
    Public Property objid As String
    '學期
    Public Property SemiYear As String
    '學期名稱
    Public Property Semistry As String
    '權責單位
    Public Property DutyOrgID As String
    '小分類
    Public Property SubClassEx As String
    '核心名稱
    Public Property Core As String    '核心編號
    '儀器編號
    Public Property ResID As String
    '儀器位置
    Public Property Location As String
    '購入年份
    Public Property BuyYear As String
    '儀器名稱(中文)
    Public Property ResName As String
    '儀器名稱(英文)
    Public Property ResNameEn As String
    '容納人數
    Public Property Contains As String


    '週別
    Public Property HourType As String
    '週次
    Public Property Week As String
    '星期
    Public Property DayOfWeek As String


    '開始日期
    Public Property StartDate As String
    '結束日期
    Public Property EndDate As String
    '開始時間
    Public Property StartTime As String
    '結束時間
    Public Property EndTime As String
    '租借對象
    Public Property BorrowerID As String
    '是否可租借
    Public Property IsRent As String
    '收費項目
    Public Property ChargeItem As String
    '時段
    Public Property TimeSlot As String
    '計費單位
    Public Property BillingUnit As String
    '金額
    Public Property Amount As String
    '其他費用
    Public Property OtherFee As String
    '保證金
    Public Property Deposit As String
    '超時時間
    Public Property OverTime As String
    '超時時間單位
    Public Property OverTimeUnit As String
    '備註
    Public Property Note As String







End Class







'<typeparam name="T">回傳資料類型</typeparam>
Public Class myResult(Of T)

    '操作是否成功
    Public Property OK As Boolean
    '回傳訊息
    Public Property MSG As String
    '回傳資料物件
    Public Property obj As T
    Public Sub New()
        Me.Clear()
    End Sub
    Public Sub Clear()
        With Me
            .OK = False
            .MSG = ""
            .obj = Nothing
        End With
    End Sub
    Public Sub Dispose()
        Me.Clear()
    End Sub
End Class





Public Class SubClass
    ''' <summary>
    ''' DB唯一識別值
    ''' </summary>
    Public Property ObjID As String
    ''' <summary>
    ''' 上層代碼
    ''' </summary>
    Public Property ParentCode As String
    ''' <summary>
    ''' 場地代碼
    ''' </summary>
    Public Property Code As String
    ''' <summary>
    ''' 場地名稱
    ''' </summary>
    Public Property Title As String
    ''' <summary>
    ''' 下層分類
    ''' </summary>
    Public Property SubClassEx As List(Of SubClass)

    Public Sub New()
        Me.Clear()
    End Sub

    Public Sub Clear()
        With Me
            .ObjID = ""
            .ParentCode = ""
            .Code = ""
            .Title = ""
            .SubClassEx = New List(Of SubClass)
        End With
    End Sub

    Public Sub Dispose()
        With Me
            .ObjID = ""
            .ParentCode = ""
            .Code = ""
            .Title = ""
            If .SubClassEx IsNot Nothing Then
                For ttI = 0 To .SubClassEx.Count - 1
                    Dim tempSC As SubClass = .SubClassEx.Item(ttI)
                    If tempSC IsNot Nothing Then
                        tempSC.Dispose()
                        tempSC = Nothing
                    End If
                Next
                .SubClassEx.Clear()
                .SubClassEx = Nothing
            End If
        End With
    End Sub

    Public Sub CopyFrom(ppWSubClass As SvcYBas.WSubClass)
        With Me
            .ObjID = ppWSubClass.ObjID
            .ParentCode = ppWSubClass.ParentCode
            .Code = ppWSubClass.Code
            .Title = ppWSubClass.Title
            If ppWSubClass.SubClassEx IsNot Nothing Then
                For ttI = 0 To ppWSubClass.SubClassEx.Count - 1
                    Dim tempSC As New SubClass
                    tempSC.CopyFrom(ppWSubClass.SubClassEx.Item(ttI))
                    .SubClassEx.Add(tempSC)
                Next
            End If
        End With
    End Sub

End Class

#End Region
















#Region "設備申請紀錄_設備申請"
Public Class YIC20_03Q
    ''' <summary>
    ''' 權責單位
    ''' </summary>
    Public Property DutyOrgID As String
    Public Property DutyOrgName As String
    ''' <summary>
    ''' 小分類
    ''' </summary>
    Public Property SubClassEx As String
    '核心名稱
    Public Property Core As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    '儀器位置
    Public Property Location As String

    ''' <summary>
    ''' 申請人
    ''' </summary>
    Public Property Borrower As String
    ''' <summary>
    ''' 起始日期
    ''' </summary>
    Public Property StartDate As String
    ''' <summary>
    ''' 起始時間
    ''' </summary>
    Public Property StartTime As String
    ''' <summary>
    ''' 結束日期
    ''' </summary>
    Public Property StopDate As String
    ''' <summary>
    ''' 結束時間
    ''' </summary>
    Public Property StopTime As String
    ''' <summary>
    ''' 狀態
    ''' </summary>
    Public Property Status As String
End Class

Public Class YIC20_03Model
    Public Property objid As String
    ''' <summary>
    ''' 權責單位
    ''' </summary>
    Public Property DutyOrgID As String
    Public Property DutyOrgName As String
    ''' <summary>
    ''' 資源類別(小分類)  例如:B0301:一般,視聽,電腦實習...
    ''' </summary>
    Public Property SubClassEx As String

    '核心名稱
    Public Property Core As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    '儀器位置
    Public Property Location As String
    '購入年份
    Public Property BuyYear As String
    '儀器名稱(中文)
    Public Property ResName As String
    '儀器名稱(英文)
    Public Property ResNameEn As String
    ''' <summary>
    ''' 起始日期
    ''' </summary>
    Public Property StartDate As String
    ''' <summary>
    ''' 起始時間
    ''' </summary>
    Public Property StartTime As String
    ''' <summary>
    ''' 結束日期
    ''' </summary>
    Public Property StopDate As String
    ''' <summary>
    ''' 結束時間
    ''' </summary>
    Public Property StopTime As String
    ''' <summary>
    ''' 開放時間(顯示在畫面上)
    ''' </summary>
    Public Property OPDTStr As String
    ''' <summary>
    ''' 租借對象(學生、教職員、校外單位)
    ''' </summary>
    Public Property BorrowerID As String
    Public Property Borrower As String
    ''' <summary>
    ''' 租借對象(租借者姓名)
    ''' </summary>
    Public Property BorrowerName As String
    ''' <summary>
    ''' 租借者連絡電話
    ''' </summary>
    Public Property Phone As String
    ''' <summary>
    ''' 備註
    ''' </summary>
    Public Property Descript As String

    ''' <summary>
    ''' 開放時段(YYXX.....)
    ''' </summary>
    Public Property Hour As String
    ''' <summary>
    ''' 審核狀態
    ''' </summary>
    Public Property Status As String
    Public Property StatusText As String
    ''' <summary>
    ''' 不通過原因
    ''' </summary>
    Public Property Reason As String

    '-----進入詳細畫面時需要先查該場地開放時段的資料------
    Public Property OpDTobjid As String
    Public Property OpDTHour As String
End Class
#End Region


#Region "貴儀中心_要審查申請單_保留"
''' <summary>
''' 申請單資料
''' </summary>
Public Class YIC20_03_Model
    Public Property objid As String
    ''' <summary>
    ''' 申請單號
    ''' </summary>
    Public Property ApplyNo As String
    ''' <summary>
    ''' 權責單位
    ''' </summary>
    Public Property DutyOrgName As String
    Public Property DutyOrgID As String

    Public Property SubClassEx As String

    '核心名稱
    Public Property Core As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    '儀器位置
    Public Property Location As String
    '購入年份
    Public Property BuyYear As String
    '儀器名稱(中文)
    Public Property ResName As String
    '儀器名稱(英文)
    Public Property ResNameEn As String
    ''' <summary>
    ''' 申請時間
    ''' </summary>
    Public Property ApplyTime As String
    ''' <summary>
    ''' 審核狀態
    ''' </summary>
    Public Property Status As String
    ''' <summary>
    ''' 申請人姓名
    ''' </summary>
    Public Property ApplicantName As String
    ''' <summary>
    ''' 申請人電話
    ''' </summary>
    Public Property ApplicantPhone As String
    ''' <summary>
    ''' 預估費用
    ''' </summary>
    Public Property EstimatedAmount As String
    ''' <summary>
    ''' 說明
    ''' </summary>
    Public Property Description As String
    ''' <summary>
    ''' 保證金
    ''' </summary>
    Public Property Deposit As String
    ''' <summary>
    ''' 選取時段
    ''' </summary>
    Public Property SelectedTimes As List(Of ApplyTimeSlot)
    Public Sub New()
        Me.Clear()
    End Sub

    Public Sub Clear()
        With Me
            .objid = ""
            .ApplyNo = ""
            .DutyOrgName = ""
            .DutyOrgID = ""
            .Location = ""
            .ApplyTime = ""
            .Status = ""
            .ApplicantName = ""
            .ApplicantPhone = ""
            .EstimatedAmount = ""
            .Description = ""
        End With
    End Sub

    Public Sub Dispose()
        Me.Clear()
        With Me
            .objid = ""
            .ApplyNo = ""
            .DutyOrgName = ""
            .DutyOrgID = ""
            .Location = ""
            .ApplyTime = ""
            .Status = ""
            .ApplicantName = ""
            .ApplicantPhone = ""
            .EstimatedAmount = ""
            .Description = ""

        End With
    End Sub
End Class

Public Class ApplyTimeSlot
    ''' <summary>
    ''' 申請單號
    ''' </summary>
    Public Property ApplyNo As String
    ''' <summary>
    ''' 申請日期
    ''' </summary>
    Public Property DateValue As String   ' yyyy-MM-dd
    ''' <summary>
    ''' 申請開始使間
    ''' </summary>
    Public Property TimeStart As String   ' HH:mm
    ''' <summary>
    ''' 申請結束時間
    ''' </summary>
    Public Property TimeEnd As String     ' HH:mm
    ''' <summary>
    ''' 狀態
    ''' </summary>
    Public Property Status As String      ' 通常為 "V"
End Class
#End Region





#Region "設備申請紀錄_場地開放時間查詢"

Public Class YIC20_03ResQ
    Public Property SemiYear As String
    Public Property Semistry As String
    ''' <summary>
    ''' 權責單位
    ''' </summary>
    Public Property DutyOrgID As String
    ''' <summary>
    ''' 小分類
    ''' </summary>
    Public Property SubClassEx As String

    '核心名稱
    Public Property Core As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    '儀器位置
    Public Property Location As String
    '購入年份
    Public Property BuyYear As String
    '儀器名稱(中文)
    Public Property ResName As String
    '儀器名稱(英文)
    Public Property ResNameEn As String
    ''' <summary>
    ''' 開始日期
    ''' </summary>
    Public Property StartDate As String
    ''' <summary>
    ''' 結束日期
    ''' </summary>
    Public Property EndDate As String
    ''' <summary>
    ''' 開始時間
    ''' </summary>
    Public Property StartTime As String
    ''' <summary>
    ''' 結束時間
    ''' </summary>
    Public Property EndTime As String
    ''' <summary>
    ''' 開放對象
    ''' </summary>
    Public Property BorrowerID As String
End Class


Public Class YIC20_03ResModel
    Public Property objid As String
    ''' <summary>
    ''' 權責單位
    ''' </summary>
    Public Property DutyOrgID As String
    Public Property DutyOrgName As String
    ''' <summary>
    ''' 資源類別(小分類)  例如:B0301:一般,視聽,電腦實習...
    ''' </summary>
    Public Property SubClassEx As String

    '核心名稱
    Public Property Core As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    '儀器位置
    Public Property Location As String
    '購入年份
    Public Property BuyYear As String
    '儀器名稱(中文)
    Public Property ResName As String
    '儀器名稱(英文)
    Public Property ResNameEn As String
    ''' <summary>
    ''' 週別
    ''' </summary>
    Public Property HourType As String
    Public Property HourTypeStr As String
    ''' <summary>
    ''' 週次
    ''' </summary>
    Public Property Week As String
    ''' <summary>
    ''' 星期
    ''' </summary>
    Public Property DayOfWeek As String
    ''' <summary>
    ''' 起始日期
    ''' </summary>
    Public Property StartDate As String
    ''' <summary>
    ''' 起始時間
    ''' </summary>
    Public Property StartTime As String
    ''' <summary>
    ''' 結束日期
    ''' </summary>
    Public Property StopDate As String
    ''' <summary>
    ''' 結束時間
    ''' </summary>
    Public Property StopTime As String
    ''' <summary>
    ''' 開放時間(顯示在畫面上)
    ''' </summary>
    Public Property OPDTStr As String
    ''' <summary>
    ''' 租借對象
    ''' </summary>
    Public Property BorrowerID As String
    Public Property Borrower As String

    ''' <summary>
    ''' 開放時段(YYXX.....)
    ''' </summary>
    Public Property Hour As String
    ''' <summary>
    ''' 容納人數
    ''' </summary>
    Public Property Contains As String
End Class





#End Region


#Region "設備申請紀錄_場地歸還"

Public Class YIC20_03Return
    Public Property objid As String
    ''' <summary>
    ''' 儀器編號
    ''' </summary>
    Public Property ResID As String
    ''' <summary>
    ''' 儀器名稱
    ''' </summary>
    Public Property ResName As String
    ''' <summary>
    ''' 租借對象(租借者姓名)
    ''' </summary>
    Public Property BorrowerName As String
    ''' <summary>
    ''' 租借者連絡電話
    ''' </summary>
    Public Property Phone As String

    ''' <summary>
    ''' 人事管理費
    ''' </summary>
    Public Property Management As String
    ''' <summary>
    ''' 預估金額
    ''' </summary>
    Public Property Price As String
    ''' <summary>
    ''' 保證金
    ''' </summary>
    Public Property Deposit As String
    ''' <summary>
    ''' 退還保證金
    ''' </summary>
    Public Property DepositRe As String
    ''' <summary>
    ''' 實際應繳金額
    ''' </summary>
    Public Property ActualPrice As String
    ''' <summary>
    ''' 歸還狀況(備註)
    ''' </summary>
    Public Property Descript As String

    ''' <summary>
    ''' 租借與歸還時間
    ''' </summary>
    Public Property ReturnTimeL As List(Of YIC20_03ReTime)
End Class

Public Class YIC20_03ReTime


    ''' <summary>
    ''' 起始日期
    ''' </summary>
    Public Property StartDate As String
    ''' <summary>
    ''' 起始時間
    ''' </summary>
    Public Property StartTime As String
    ''' <summary>
    ''' 結束日期
    ''' </summary>
    Public Property StopDate As String
    ''' <summary>
    ''' 結束時間
    ''' </summary>
    Public Property StopTime As String
    ''' <summary>
    ''' 申請使用時間(顯示在畫面上)
    ''' </summary>
    Public Property ApplyTimeStr As String

    ''' <summary>
    ''' 歸還日期
    ''' </summary>
    Public Property ReturnDate As String
    ''' <summary>
    ''' 歸還時間
    ''' </summary>
    Public Property ReturnTime As String
    ''' <summary>
    ''' 歸還時間(顯示在畫面上)
    ''' </summary>
    Public Property ReturnTimeStr As String



End Class

Class YIC20_03Calendar
    Public Property Core As String
    Public Property ResName As String
    Public Property BorrowerName As String
    Public Property StartDate As String
    Public Property StartTime As String
    Public Property StopDate As String
    Public Property StopTime As String

End Class





#End Region

















﻿@Code
    If Request.IsAjaxRequest Then
        Layout = Nothing
    End If
    Dim ttSemiYear As String = ""
    Dim ttSemistry As String = "1"

    Dim id As String = "_" + Guid.NewGuid.ToString

    ''Dim ttSCL As List(Of TMyCode) = ViewData("SubClass")
End Code


<div>
    <div class="View1">
        <div id="divfixed1">
            @*功能名稱*@
            <span class="page100-form-label p-b-5 bo1-ColorM" style="color:black;">
                @ViewData("SubFuncTitle")
            </span>

            @*注意事項*@
            @*<div class="row col-lg-12 col-md-12 col-sm-12 col-xs-12"></div>*@

            @*按鈕*@
            <div class="row">
                <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">
                    <input id="btExit" type="button" class="btn btn-default btn-Color1" value="返回" />
                    <input id="btSave" type="button" class="btn btn-secondary btn-Color2" value="儲存" />
                    <input id="btSent" type="button" class="btn btn-secondary btn-Color2" value="送出" />
                </div>
            </div>
            @*查詢條件*@

            <div class="b2k-control">
                <div Class="row">
                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12">



                        <label>權責單位：</label>
                        <select id="ddl_DutyOrg" class="ResQry">
                            <option value="A50">貴儀中心</option>

                        </select>

                        <label>申請帳號類型：</label>
                        <select id="ddl_AccType " class="ResQry">
                            <option value="">廠商申請</option>
                        </select>

                        <label>申請人帳號：</label>
                        <input type="text" id="tx_Acc" style="width: 180px;" />


                        <label>申請人單位：</label>
                        <input type="text" id="tx_Dept" style="width: 180px;" />


                        <label>公司名稱：</label>
                        <input type="text" id="tx_Name" style="width: 180px;" />

                        <label>公司名稱(英)：</label>
                        <input type="text" id="tx_NameEn" style="width: 180px;" />


                        <label>統一編號：</label>
                        <input type="text" id="tx_TaxId" style="width: 180px;" />


                        <label>公司電話：</label>
                        <input type="text" id="tx_Tel" style="width: 180px;" />


                        <label>分機：</label>
                        <input type="text" id="tx_Ext" style="width: 180px;" />

                        <label>行動電話：</label>
                        <input type="text" id="tx_Mobile" style="width: 180px;" />



                        <label>LineID：</label>
                        <input type="text" id="tx_LineID" style="width: 180px;" />

                        <div style="display: flex; align-items: center; flex-wrap: wrap; margin-bottom: 8px;">
                            <label style="margin-right: 4px;">公司地址：</label>
                            <input type="text" id="tx_City" style="margin-right: 4px;" />
                            <input type="text" id="tx_Area" style="margin-right: 8px;" />
                            <input type="text" id="tx_Address" style="width: 240px; margin-right: 8px;" />
                            <label style="margin-right: 4px;">郵遞區號：</label>
                            <input type="text" id="tx_PostalCode" style="width: 80px;" />
                        </div>



                        <div style="display: flex; align-items: center; flex-wrap: wrap;">
                            <label style="color:red; margin-right:4px;">*E-Mail：</label>
                            <input type="text" id="tx_Email" style="width: 240px; margin-right:8px;" />
                            <button type="button" id="btnChkEmail" style="margin-right:8px;">驗證信箱</button>
                            <span style="margin-right:16px;">例如：<EMAIL></span>

                            <label style="margin-right:8px;">附件：</label>
                            <button type="button" id="btnUploadFile" style="margin-right:8px;">上傳檔案</button>
                            <span style="margin-right:16px;">*MOU或切結書</span>
                            <button type="button" id="btnDownloadSample" style="margin-right:8px;">
                                下載範例(切結書)
                            </button>
                        </div>

                        <div style="display: flex; align-items: center; flex-wrap: wrap; margin-bottom: 8px;">
                            <label style="margin-right: 4px;">負責人姓名：</label>
                            <input type="text" id="tx_ContactName" style="width: 180px; margin-right: 32px;" />
                            <label style="margin-right: 4px;">負責人 Email：</label>
                            <input type="text" id="tx_ContactEmail" style="width: 240px;" />
                            <label style="margin-right: 4px;">門禁卡號：</label>
                            <input type="text" id="tx_AccessCardNo" style="width: 180px;" />
                        </div>




                    </div>
                    <div style="display: flex; align-items: flex-start; flex-wrap: wrap; margin-top: 8px;">
                        <label style="margin-right: 4px;">備註/其他說明：</label>
                        <textarea id="tx_Remark" style="width: 90%; min-width: 320px; min-height: 60px; resize: vertical;"></textarea>
                    </div>







                </div>


            </div>
        </div>
    </div>

    <script type="text/javascript">
    (function ($) {
        $(function () {
            var sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
            var ss_Header_height = $('.limiter').height(); //__Header.vbhtml的高度
            var ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
            var ssfixed1_height = $('#@id #divfixed1').height(); //除了資料清單表資料(功能名稱、注意事項、....)的高度
            var sstabsul_height; //tabs標籤的高度
            var ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表-歷年成績的高度
            var ssNfixed2_height = ssView1_height - ssfixed1_height; //資料清單表-修業清單的高度
            var ssbFilter_height = 31; //搜尋高度:固定高度無須調整
            var ssInfoEmpty_height = 20; //顯示筆數高度(顯示第 0 至 0 項結果，共 0 項):固定高度無須調整
            var ssTb1header_height = 100; //資料清單表頭高度:不固定請自行調整(當表頭越高，值越大，相反的，當表頭越低，值越小)
            var ssTb1EveryScore; //資料清單表-歷年成績
            var ssTb1StudyList; //資料清單表-修業清單
            var ssTabIdx = '';

            var changeSaved = true;
            let isMouseDown = false; // 是否按下滑鼠
            let dragMode = null;     // 要改為 O 或 X 的狀態
            let currentDate = new Date(); // 初始為今天
            let currentWeekStart = null;  // 這週週一
            let currentWeekEnd = null;    // 這週週日


            @*var ttobjid = '@Model.objid'
            var ttOpDTobjid = '@Model.OpDTobjid'*@
            //-----畫面執行-----


            //------------------

            //-----觸發事件-----
            //查詢場地
            $('#@id .ResQry').on('change', function () {
                QryRes();
            });

            $(window).resize(function () {
                ReSize();
            });
            //------------------

            //-----click事件-----
            //返回
            $(' #btExit').click(function () {
                $(this).trigger('YIC30Apply_Close');
            });

            //儲存
            $(' #btSave').click(function () {
                alert("儲存成功");
            });

            // 上一週

            // 下一週

            //----------------------
            //-------------------

            //-----欄位格式設定-----
            //----------------------

            //------dialog------
            //------------------

            //-----function-----
            function QryOpDT() {
                var ttobj = {};
                ttobj.OpDTobjid = ttOpDTobjid;
                var urlJSON = '@Url.Action("YXA30Detail_QryOpDT", "YXA30")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            $('#@id #tx_OpDtobjid').val(data.obj.OpDTobjid);
                            $('#@id #ddl_DutyOrg').prop('disabled', true);
                            $('#@id #ddl_SubClassEx').prop('disabled', true);
                            $('#@id #ddl_Res').prop('disabled', true);

                            $('#@id #ddl_DutyOrg').val(data.obj.DutyOrgID);

                            // 第一次觸發 ResQry 的 change（會呼叫 QryRes）
                            QryRes().then(function () {
                                // 等第一次 QryRes 結束後
                                $('#@id #ddl_SubClassEx').val(data.obj.SubClassEx);

                                // 再觸發一次 ResQry 的 change
                                return QryRes();
                            }).then(function () {
                                // 等第二次 QryRes 結束後
                                $('#@id #ddl_Res').val(data.obj.ResID);
                            });
                            buildTimeTable();
                            enableDragSelection();    // 啟用拖曳功能
                            enableControlButtons();   // 啟用控制按鈕
                            if (data.obj.OpDTHour != '' && data.obj.OpDTHour != undefined) {
                                applyScheduleFromString(data.obj.OpDTHour, "O");
                            }

                            Qry();
                        }
                        else {
                            alert(data.MSG);
                        };
                    }
                })
            }

            function Qry() {
                var ttobj = {};
                ttobj.objid = ttobjid;
                var urlJSON = '@Url.Action("YXA30Detail_Qry", "YXA30")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            if (ttobjid == '-1') {
                                $('#@id #ddl_Borrower').val('');
                                $('#@id #tx_BorrowerName').val('');
                                $('#@id #tx_Phone').val('');

                                $('#@id #tx_Deposit').val('');
                                $('#@id #tx_Management').val('');
                                $('#@id #tx_Price').val('');

                                $('#@id #tx_Descript').val('');
                            } else {
                                $('#@id #ddl_Borrower').val(data.obj.BorrowerID);
                                $('#@id #tx_BorrowerName').val(data.obj.BorrowerName);
                                $('#@id #tx_Phone').val(data.obj.Phone);
                                $('#@id #tx_Descript').val(data.obj.Descript);
                            }

                            if (data.obj.Hour != '' && data.obj.Hour != undefined) {
                                applyScheduleFromString(data.obj.Hour,"V");
                            }

                            //CreateTbl($('#@id #TblDetail'), false, (ssNfixed1_height - ssbFilter_height - ssInfoEmpty_height - ssTb1header_height));
                        }
                        else {
                            alert(data.MSG);
                        };
                    }
                })
            }

            function Save() {
                var ttobj = {};
                ttobj.objid = ttobjid;
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                ttobj.SubClassEx = $('#@id #ddl_SubClassEx').val();
                ttobj.ResID = $('#@id #ddl_Res').val();
                ttobj.BorrowerID = $('#@id #ddl_Borrower').val();
                ttobj.BorrowerName = $('#@id #tx_BorrowerName').val();
                ttobj.Phone = $('#@id #tx_Phone').val();
                ttobj.Descript = $('#@id #tx_Descript').val();

                var StartDate = formatDateToNumber(currentWeekStart);
                var StopDate = formatDateToNumber(currentWeekEnd);
                ttobj.StartDate = StartDate;
                ttobj.StopDate = StopDate;
                ttobj.Hour = "";
                // 針對 #TbTime 中的 .time-cell 掃描，將其轉為Y/X的格式
                for (let row = 0; row < 48; row++) {
                    for (let col = 0; col < 7; col++) {
                        debugger
                        const cell = $(`#TbTime .time-cell[data-row='${row}'][data-col='${col}']`);
                        const status = cell.text().trim();//cell.data("status");

                        if (status == "V") {
                            ttobj.Hour += "Y";
                        } else {
                            ttobj.Hour += "X";
                        }
                    }
                }

                var urlJSON = '@Url.Action("YXA30Detail_Save", "YXA30")';
                $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppobj: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            alert("儲存成功");
                            ttobjid = data.obj.objid;
                            Qry();
                        }
                        else {
                            alert(data.MSG);
                        };
                    }
                })
            }

            //查詢物件(場地/設備)
            function QryRes() {
                var ttobj = {};
                ttobj.DutyOrgID = $('#@id #ddl_DutyOrg').val();
                ttobj.SubClassEx = $('#@id  #ddl_SubClassEx').val();

                var urlJSON = '@Url.Action("F_GetCode_Res", "YXA30")';
                return $.ajax({
                    type: 'POST',
                    url: urlJSON,
                    data: {
                        ppqmodel: ttobj
                    },
                    success: function (data) {
                        if (data.OK) {
                            var ttOption = '<option value="">請選擇</option>';
                            $.map(data.obj, function (item) {
                                ttOption += '<option value="' + item.Code + '">' + item.Title + '</option>';
                            });

                            $('#@id #ddl_Res').find('option').detach();
                            $('#@id #ddl_Res').append(ttOption);
                        } else {
                            alert(data.MSG);
                        }
                    }
                });
            }


            //根據傳入資料在對應的欄位上打O或V
            function applyScheduleFromString(scheduleStr,ppText) {

                $(".time-cell").each(function () {
                    const $cell = $(this);
                    const row = parseInt($cell.attr("data-row"));
                    const col = parseInt($cell.attr("data-col"));
                    const index = row * 7 + col;

                    const ch = scheduleStr.charAt(index);
                    if (ch === "Y") {
                        //console.log(`→ 第 ${index} 格 (row=${row}, col=${col}) 設為 O`);
                        setCellStatus($cell, ppText);
                    }
                });
            }

            function buildTimeTable(baseDate = null) {
                const days = ['一', '二', '三', '四', '五', '六', '日'];
                const tbody = $('#TbTime tbody');
                const theadRow = $('#theadRow');

                theadRow.empty();
                tbody.empty();

                theadRow.append('<th style="padding:1em;">時間</th>');

                // 使用傳入的 baseDate 或 currentDate
                const today = baseDate ? new Date(baseDate) : new Date(currentDate);
                const dayOfWeek = today.getDay(); // 0=Sun, 1=Mon
                const offset = (dayOfWeek + 6) % 7; // 調整為週一 = 0
                const monday = new Date(today);
                monday.setDate(today.getDate() - offset);
                currentWeekStart = new Date(monday);
                currentWeekEnd = new Date(monday);
                currentWeekEnd.setDate(monday.getDate() + 6);

                // 產生表頭（週幾 + 月日）
                for (let i = 0; i < 7; i++) {
                    const curr = new Date(monday);
                    curr.setDate(monday.getDate() + i);
                    const yyyy = curr.getFullYear();
                    const mm = (curr.getMonth() + 1).toString().padStart(2, '0');
                    const dd = curr.getDate().toString().padStart(2, '0');
                    theadRow.append(`<th style="padding:1em;">週${days[i]}<br>${yyyy}/${mm}/${dd}</th>`);
                }

                // 時間格（每半小時）
                for (let h = 0; h < 24; h++) {
                    for (let m = 0; m < 60; m += 30) {
                        const start = `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}`;
                        const endH = (m + 30 >= 60) ? h + 1 : h;
                        const endM = (m + 30) % 60;
                        const end = `${endH.toString().padStart(2, '0')}:${endM.toString().padStart(2, '0')}`;
                        const timeRange = `${start}~${end}`;

                        let row = `<tr><td style="padding:1em;">${timeRange}</td>`;
                        for (let d = 0; d < 7; d++) {
                            row += `<td class="time-cell" data-row="${(h * 2 + m / 30)}" data-col="${d}" data-status="none"></td>`;
                        }
                        row += '</tr>';
                        tbody.append(row);
                    }
                }
            }

            // 設定格子的狀態（O、X、none)
            function setCellStatus(cell, status) {
                let $cell = $(cell);
                $cell.data("status", status); // 存狀態

                if (status === "V") {
                    $cell.text("V").removeClass("bg-success").addClass("bg-warning");
                } else if (status === "O") {
                    $cell.text("O").removeClass("bg-warning").addClass("bg-success");
                } else {
                    $cell.text("").removeClass("bg-success bg-danger"); // 清空狀態與樣式
                }
            }

            //借用者選取格子狀態(V)
            function toggleUserSelection(cell) {
                let $cell = $(cell);
                let origStatus = $cell.text().trim();//$cell.data("orig-status"); // 原始狀態

                if (origStatus == "O") {
                    //還沒選過 → 設成 V（黃底）
                    $cell.text("V").removeClass("bg-success").addClass("bg-warning");
                } else if (origStatus == "V") {
                    //已經選取過，取消選取 → 還原成 O（綠底）
                    $cell.text("O").removeClass("bg-warning").addClass("bg-success");
                }
            }

            // 啟用拖曳與點選選取功能（必須先選擇模式）
            function enableDragSelection() {
                $(document).off("click", ".time-cell");
                //使用者點選
                $(document).on("click", ".time-cell", function () {
                    toggleUserSelection(this);
                    changeSaved = false;
                    //updateEstimatedCharge();
                });
            }

            // 控制按鈕事件（O/X/清除）
            function enableControlButtons() {
                // 清除所有格子的狀態與外觀
                $("#btClear").click(function () {
                    //$(".time-cell").each(function () {
                    //    setCellStatus($(this), "none");
                    //});
                    //dragMode = null;
                    // 按鈕視覺恢復未選擇狀態
                    //$("#btnSetO, #btnSetX").removeClass("active");

                    $(".time-cell").each(function () {
                        if ($(this).text().trim() == "V") {
                            $(this).text("O").removeClass("bg-warning").addClass("bg-success");
                        }
                    });
                    changeSaved = false
                });
            }

            //日期轉數字 EX:Mon Jul 28 2025 16:28:39 GMT+0800 (台北標準時間) > 20250728
            function formatDateToNumber(date) {
                const y = date.getFullYear();
                const m = (date.getMonth() + 1).toString().padStart(2, '0');
                const d = date.getDate().toString().padStart(2, '0');
                return `${y}${m}${d}`; // e.g. 20250718
            }
            //----------

            function CreateTbl(ppTb1, ppbFilter, ppscrollY) {
                ssTb1 = ppTb1.DataTable({
                    "bFilter": ppbFilter,  //搜尋
                    "bLengthChange": true,
                    "paging": false,        //分頁
                    "bAutoWidth": false,
                    "fixedHeader": true, //表頭固定
                    "scrollY": ppscrollY, //超過(設定值)時，顯示卷軸
                    //"scrollX": auto, //auto
                    //固定首列，需要引入相應的dataTables.fixedColumns.min.js
                    "fixedColumns": {
                        "leftColumns": 1 //最左側1列固定
                    },

                    deferRender: true,
                    destroy: true,
                    scroller: true,
                    responsive: false
                });
            }

            function ReSize() {
                sswindow_height = $(window).height(); //整個瀏覽器視窗的高度
                ssView1_height = (sswindow_height - ss_Header_height); //View1的高度
                ssNfixed1_height = ssView1_height - ssfixed1_height; //資料清單表-歷年成績的高度
                ssNfixed2_height = ssView1_height - ssfixed1_height; //資料清單表-修業清單的高度
                $('#@id .View1').css('height', ssView1_height + 'px');
                $('#@id #divNfixed1').css('height', ssNfixed1_height + 'px');
                $('#@id #divNfixed2').css('height', ssNfixed2_height + 'px');
                if ((ssTabIdx == '') || (ssTabIdx == '0')) {

                } else {

                }
            }
            //------------------

        });
    })(jQuery);

    </script>

</div>
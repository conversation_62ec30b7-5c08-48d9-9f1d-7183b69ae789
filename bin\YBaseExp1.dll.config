﻿<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections/>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <add key="ForceReload" value="2025-08-31-v5"/>
  </appSettings>
  <!--
    如需 web.config 變更的說明，請參閱 http://go.microsoft.com/fwlink/?LinkId=235367。

    您可以在 <httpRuntime> 標記上設定下列屬性。
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <!--<identity userName="BridgeSysUser" password="bDk_Eu@28Dd@j" impersonate="true" />-->
    
    <!-- 原始設定 (已備註) -->
    <!--<compilation debug="true" targetFramework="4.7.2"/>-->
    
    <!-- 第一次修正 (已備註) - assemblies配置造成IIS錯誤 -->
    <!--
    <compilation debug="true" targetFramework="4.7.2">
      <assemblies>
        <add assembly="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
        <add assembly="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31BF3856AD364E35" />
      </assemblies>
    </compilation>
    -->
    
    <!-- 第二次修正 - 簡化配置，移除可能造成衝突的assemblies -->
    <compilation debug="true" targetFramework="4.7.2" />
    
    <!-- 添加 MVC 頁面設定 -->
    <pages>
      <namespaces>
        <add namespace="System.Web.Mvc" />
        <add namespace="System.Web.Mvc.Ajax" />
        <add namespace="System.Web.Mvc.Html" />
        <add namespace="System.Web.Routing" />
        <add namespace="System.Web.Optimization" />
      </namespaces>
    </pages>
    
    <!--maxRequestLength 檔案大小(KB)，預設值4096 KB(4MB)，所以10240KB為10MB-->
    <!--executionTimeout 上傳時間(秒)，300秒為5分鐘-->
    <!-- 原始設定 (已備註) - targetFramework版本不一致 -->
    <!--<httpRuntime targetFramework="4.6.1" maxRequestLength="10240" executionTimeout="300"/>-->
    
    <!-- 修正後的設定 - 統一使用 4.7.2 版本 -->
    <httpRuntime targetFramework="4.7.2" maxRequestLength="10240" executionTimeout="300"/>
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
    </httpModules>
  </system.web>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.1" newVersion="4.0.2.1"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules>
      <remove name="TelemetryCorrelationHttpModule"/>
      <add name="TelemetryCorrelationHttpModule" type="Microsoft.AspNet.TelemetryCorrelation.TelemetryCorrelationHttpModule, Microsoft.AspNet.TelemetryCorrelation" preCondition="integratedMode,managedHandler"/>
      <remove name="ApplicationInsightsWebTracking"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler"/>
    </modules>
  </system.webServer>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701"/>
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+"/>
    </compilers>
  </system.codedom>
  <system.serviceModel>
    <bindings>
      <basicHttpBinding>
        <binding name="BasicHttpBinding_ISvcYBas" />
        <binding name="BasicHttpBinding_ISvcYCos" />
        <binding name="BasicHttpBinding_ISvcYLog" />
        <binding name="BasicHttpBinding_ISvcYBase" />
        <binding name="BasicHttpsBinding_ISvcYBase">
          <security mode="Transport" />
        </binding>
      </basicHttpBinding>
    </bindings>
    <client>
      <endpoint address="http://localhost/WCFY/WcfYBas/SvcYBas.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISvcYBas"
        contract="SvcYBas.ISvcYBas" name="BasicHttpBinding_ISvcYBas" />
      <endpoint address="http://localhost/WCFY/WcfYCos/SvcYCos.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISvcYCos"
        contract="SvcYCos.ISvcYCos" name="BasicHttpBinding_ISvcYCos" />
      <endpoint address="http://localhost/WCFY/WcfYLog/SvcYLog.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISvcYLog"
        contract="SvcYLog.ISvcYLog" name="BasicHttpBinding_ISvcYLog" />
      <endpoint address="http://win-32g/SvcY/SvcYBase/SvcYBase.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISvcYBase"
        contract="SvcYsBase.ISvcYBase" name="BasicHttpBinding_ISvcYBase" />
      <endpoint address="https://infobridge.com.tw/SvcY/SvcYBase/SvcYBase.svc"
        binding="basicHttpBinding" bindingConfiguration="BasicHttpsBinding_ISvcYBase"
        contract="SvcYsBase.ISvcYBase" name="BasicHttpsBinding_ISvcYBase" />
    </client>
  </system.serviceModel>
</configuration>
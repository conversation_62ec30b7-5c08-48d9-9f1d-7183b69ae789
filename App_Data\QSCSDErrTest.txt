# YBaseExp1 错误日志文件
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, and "(" are valid at the start of a code block and they must occur immediately following "@" with no space in between.

A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, and "(" are valid at the start of a code block and they must occur immediately following "@" with no space in between.

A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, and "(" are valid at the start of a code block and they must occur immediately following "@" with no space in between.

The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/SelCosGroup/SelCosGroup' was not found or does not implement IController.
The controller for path '/SelPeriod/SelPeriod' was not found or does not implement IController.
A public action method 'SelMultiOrgV2' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
A public action method 'SelMultiOrg' was not found on controller 'YBaseExp1.Controllers.SelOrgController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/YBaseExpDetail' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/Logout' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
A public action method 'EditEquipment' was not found on controller 'YBaseExp1.Controllers.InstrumentCenterController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
A public action method 'qrydata' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
A public action method 'qrydata' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
A public action method 'qrydata' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/ReUrlContent' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, and "(" are valid at the start of a code block and they must occur immediately following "@" with no space in between.

The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
C:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\3144cfb7\fff45bf2\App_Web_callendar.vbhtml.1c721a76.r0vpdj9d.0.vb(56): error BC30451: 'fullcalendar' is not declared. It may be inaccessible due to its protection level.
"@" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

"@" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

"@" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
A public action method 'YXA20Detail' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
A public action method 'YXA20Detail' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
A public action method 'YXA20Detail' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
A public action method 'YXA20Detail' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YIC20/YXA20Main_Qry' was not found or does not implement IController.
The controller for path '/YIC20/YXA20Main_Qry' was not found or does not implement IController.
The controller for path '/YIC20/YXA20Main_Qry' was not found or does not implement IController.
A public action method 'YXA20Detail' was not found on controller 'YBaseExp1.Controllers.YBaseExpController'.
The controller for path '/YIC20/YXA20Main_Qry' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettongs_Qry' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettongs_Qry' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettongs_Qry' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettings' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettings' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YIC20/OpenTimeSettings' was not found or does not implement IController.
The controller for path '/' was not found or does not implement IController.
The controller for path '/YBaseExp/YXA20Detail' was not found or does not implement IController.
The controller for path '/YBaseExp/YXA20Detail' was not found or does not implement IController.
The controller for path '/YBaseExp/YXA20Detail' was not found or does not implement IController.
A public action method 'OpenTimeSettingsDetail' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
The controller for path '/YBaseExp/OpenTimeSettings' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandardNewAdd' was not found or does not implement IController.
The controller for path '/YBaseExp/QryInstrumentListChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandardNewAdd' was not found or does not implement IController.
A public action method 'YBaseExpMain' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC30\YIC30Detail.vbhtml(48): error BC30451: 'ttSCL' is not declared. It may be inaccessible due to its protection level.
C:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\3144cfb7\fff45bf2\App_Web_yic30detail.vbhtml.837751b1._8tq9siy.0.vb(55): error BC30451: 'id' is not declared. It may be inaccessible due to its protection level.
C:\Users\<USER>\AppData\Local\Temp\Temporary ASP.NET Files\vs\3144cfb7\fff45bf2\App_Web_yic30detail.vbhtml.837751b1.w9vrmtp6.0.vb(55): error BC30451: 'id' is not declared. It may be inaccessible due to its protection level.
A public action method ' YIC30Main' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method ' YIC30Main' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method 'YIC30Detail_ResearchAppl' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method 'YIC30Detail_ResearchAppl' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method 'YIC30Detail_ResearchAppl' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method 'YIC30Detail_ResearchAppl' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
A public action method 'YIC30Detail_ResearchAppl' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
The controller for path '/YXA20/YXA20Main_Del' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Main_Del' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Main_Del' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Main_Del' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Main_Del' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20Detail_Qry' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The controller for path '/YBaseExp/ChargeStandard' was not found or does not implement IController.
The view 'ChargeStandardNewAdd' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC20/ChargeStandardNewAdd.aspx
~/Views/YIC20/ChargeStandardNewAdd.ascx
~/Views/Shared/ChargeStandardNewAdd.aspx
~/Views/Shared/ChargeStandardNewAdd.ascx
~/Views/YIC20/ChargeStandardNewAdd.cshtml
~/Views/YIC20/ChargeStandardNewAdd.vbhtml
~/Views/Shared/ChargeStandardNewAdd.cshtml
~/Views/Shared/ChargeStandardNewAdd.vbhtml
The view 'ChargeStandardNewAdd' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC20/ChargeStandardNewAdd.aspx
~/Views/YIC20/ChargeStandardNewAdd.ascx
~/Views/Shared/ChargeStandardNewAdd.aspx
~/Views/Shared/ChargeStandardNewAdd.ascx
~/Views/YIC20/ChargeStandardNewAdd.cshtml
~/Views/YIC20/ChargeStandardNewAdd.vbhtml
~/Views/Shared/ChargeStandardNewAdd.cshtml
~/Views/Shared/ChargeStandardNewAdd.vbhtml
The view 'ChargeStandardNewAdd' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC20/ChargeStandardNewAdd.aspx
~/Views/YIC20/ChargeStandardNewAdd.ascx
~/Views/Shared/ChargeStandardNewAdd.aspx
~/Views/Shared/ChargeStandardNewAdd.ascx
~/Views/YIC20/ChargeStandardNewAdd.cshtml
~/Views/YIC20/ChargeStandardNewAdd.vbhtml
~/Views/Shared/ChargeStandardNewAdd.cshtml
~/Views/Shared/ChargeStandardNewAdd.vbhtml
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardNewAdd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
The "If" block was not terminated.  All "If" statements must be terminated with a matching "End If".

A public action method 'ChargeStandardDetail' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03_Qry' was not found or does not implement IController.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\Callendar.vbhtml(25): error BC30112: 'System.Media' is a namespace and cannot be used as an expression.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
A public action method 'Logout' was not found on controller 'YBaseExp1.Controllers.YIC30Controller'.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
A public action method 'ChargeStandardDetail' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Res' was not found or does not implement IController.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\Callendar.vbhtml(30): error BC30112: 'System.Media' is a namespace and cannot be used as an expression.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YIC20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YIC20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YIC20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_QryOpDT' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_QryOpDT' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The view 'ValidateApplication' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC20/ValidateApplication.aspx
~/Views/YIC20/ValidateApplication.ascx
~/Views/Shared/ValidateApplication.aspx
~/Views/Shared/ValidateApplication.ascx
~/Views/YIC20/ValidateApplication.cshtml
~/Views/YIC20/ValidateApplication.vbhtml
~/Views/Shared/ValidateApplication.cshtml
~/Views/Shared/ValidateApplication.vbhtml
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20Main.vbhtml(376): error BC30002: Type 'mDocCheck' is not defined.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20Main.vbhtml(376): error BC30002: Type 'mDocCheck' is not defined.
The controller for path '/YXA20/YXA20_03_ShenCha' was not found or does not implement IController.
The controller for path '/YBaseExp1/LogoutApFuncLog' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20_03Return.vbhtml(55): error BC30456: 'ResName' is not a member of 'YIC20_03Return'.
The view 'YIC30Res' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC30/YIC30Res.aspx
~/Views/YIC30/YIC30Res.ascx
~/Views/Shared/YIC30Res.aspx
~/Views/Shared/YIC30Res.ascx
~/Views/YIC30/YIC30Res.cshtml
~/Views/YIC30/YIC30Res.vbhtml
~/Views/Shared/YIC30Res.cshtml
~/Views/Shared/YIC30Res.vbhtml
The view 'YIC30Res' or its master was not found or no view engine supports the searched locations. The following locations were searched:
~/Views/YIC30/YIC30Res.aspx
~/Views/YIC30/YIC30Res.ascx
~/Views/Shared/YIC30Res.aspx
~/Views/Shared/YIC30Res.ascx
~/Views/YIC30/YIC30Res.cshtml
~/Views/YIC30/YIC30Res.vbhtml
~/Views/Shared/YIC30Res.cshtml
~/Views/Shared/YIC30Res.vbhtml
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
"{" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

"{" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

"{" is not valid at the start of a code block.  Only identifiers, keywords, comments, and "(" are valid.

The controller for path '/YBaseExp1/Logout' was not found or does not implement IController.
The controller for path '/YBaseExp1/Logout' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
End of file was reached before the end of the block comment.  All comments that start with the "@*" sequence must be terminated with a matching "*@" sequence.

A public action method 'dv_eq_acd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'dv_eq_acd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'dv_eq_acd' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardDetail' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
A public action method 'ChargeStandardDetail' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.
The controller for path '/YBaseExp1/Logout' was not found or does not implement IController.
The controller for path '/YBaseExp1/Logout' was not found or does not implement IController.
D:\Code\VS2017\250705\YBaseExp1\YBaseExp1\Views\YIC20\YIC20MainDetail.vbhtml(655): error BC30112: 'System.Media' is a namespace and cannot be used as an expression.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
The controller for path '/YXA20/YXA20_03Detail_Qry' was not found or does not implement IController.
A space or line break was encountered after the "@" character.  Only valid identifiers, keywords, comments, and "(" are valid at the start of a code block and they must occur immediately following "@" with no space in between.

A public action method 'GetCalendarEvents' was not found on controller 'YBaseExp1.Controllers.YIC20Controller'.

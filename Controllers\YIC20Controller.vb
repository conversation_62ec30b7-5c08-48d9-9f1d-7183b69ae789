﻿Imports System.Linq
Namespace Controllers
    Public Class YIC20Controller
        Inherits MyController


#Region "YIC20模組功能"

        Function Atest() As ActionResult

            ViewData("FuncTitle") = "test"
            Return View()
        End Function



#End Region


#Region "貴重儀器中心_設備管理"

        Function YIC20Main() As ActionResult

            ViewData("FuncTitle") = "貴重儀器中心_設備管理"
            Return View()
        End Function


        Function YIC20MainDetail() As ActionResult

            ViewData("FuncTitle") = "貴重儀器中心_設備詳細資料"
            Return View()
        End Function






        'Mock 設備管理查詢 - 用於雛型系統測試
        <HttpPost>
        Public Function InstrumentList_Qry(ppqmodel As YXA20QModel) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String
            Dim ttMyList As New List(Of YXA20QModel)
            Dim ttErrStr As New List(Of String)
            Try
                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "InstrumentListChargeStandar_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Qry", "SvcYAcc", "QryTBResOpenTime")                
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GenerateMockYXA20QData(ppqmodel)
                End If

                '創建ttMyList(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    '*查資料
                '    'Dim client = GetSvcYAccClient()
                '    ' 建立查詢條件物件
                '    Dim ttWQobj As New MockWQTBResOpenTime() With {
                '    ' 回傳資料 並調整格式
                '    'ppqmodel轉為ttWQobj
                '    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                '                ttMyList.Add(ttsobj)
                '            Next
                '        End If

                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                '    Session("ssGradeOpenL") = ttMyList
                'End If
                '結束ttMyList

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If
            Return ttResult
        End Function



#End Region

#Region "貴重儀器中心_開放時間設定"
        Function OpenTimeSettings() As ActionResult
            ViewData("FuncTitle") = "貴重儀器中心_開放時間查詢"

            Return View()
        End Function


        '取得SubClass(Real)
        'Public Function F_GetCode_SubClass(ByVal ppinfono As String, ByRef ppCodeS As List(Of TMyCode)) As String
        '    Dim ttErrStr As New List(Of String)
        '    Dim ttRetStr As String = ""
        '    Dim ttRetObj As New List(Of Object)
        '    Dim ttRetVal As String = ""
        '    Dim ttsvcLog = GetSvcYLogClient()
        '    Dim SvcYBas As SvcYBas.SvcYBasClient = GetSvcYBasClient()

        '    Try
        '        Dim ttYLogPLD As SvcYLog.WGetTokenParam = Nothing
        '        ''*固定
        '        '方法2:每次呼叫WCF都要New出來,SetWYLogPLDF 不New
        '        ttYLogPLD = New SvcYLog.WGetTokenParam
        '        ttYLogPLD.WGetTokenParamF = New SvcYLog.WGetTokenParamF
        '        SetWYLogPLDF(ttYLogPLD.WGetTokenParamF)
        '        '*動態
        '        ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
        '        SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "F_GetCode_SubClass", "SvcYBas", "GetCodeS2")
        '        ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
        '        If Not ttRetStr.StartsWith("OK") Then
        '            ttErrStr.Add(ttRetStr)
        '        End If
        '        If ttErrStr IsNot Nothing AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
        '            Dim client = GetSvcYBasClient()
        '            '*查資料
        '            Dim ttWPLD As New SvcYBas.WPLD
        '            GetWPLDF(ttWPLD)
        '            ttWPLD.WPLDD = New SvcYBas.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
        '            WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

        '            Dim ttWObj As New List(Of SvcYBas.WCode)
        '            Dim ttWQObj As New SvcYBas.WCode
        '            ttWQObj.CodeClass = "SubClassEx"

        '            ttRetStr = client.GetCodeS2(ttWPLD, ppinfono, ttWQObj, ttWObj)

        '            If Not ttRetStr.StartsWith("OK") Then
        '                ttErrStr.Add(ttRetStr)
        '            Else
        '                '*WCF
        '                If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
        '                    For Each ttrobj In ttWObj
        '                        Dim ttobj As New TMyCode
        '                        ttobj.Code = ttrobj.Code
        '                        ttobj.Title = ttrobj.Title
        '                        ppCodeS.Add(ttobj)
        '                    Next
        '                End If
        '            End If
        '        End If

        '    Catch ex As Exception
        '        ttErrStr.Add(ex.Message)
        '    Finally
        '        ttsvcLog.Close()
        '        ttsvcLog = Nothing
        '        SvcYBas.Close()
        '        SvcYBas = Nothing
        '    End Try
        '    If ttErrStr.Count = 0 Then
        '        ttRetVal = "OK"
        '    Else
        '        ttRetVal = String.Join("、", ttErrStr.ToArray)
        '        ttErrStr.Clear()
        '    End If
        '    ttErrStr = Nothing
        '    Return ttRetVal
        'End Function








        'Mock 查詢開放時間設定 - 用於雛型系統測試
        <HttpPost>
        Public Function OpenTimeSettings_Qry(ppqmodel As YXA20Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String
            Dim ttMyList As New List(Of YXA20Model)
            Dim ttErrStr As New List(Of String)
            Try
                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "OpenTimeSettongs_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Qry", "SvcYAcc", "QryTBResOpenTime")                
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GenerateMockYXA20Data(ppqmodel)
                End If
                '創建ttMyList(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    '*查資料
                '    ' 模擬寫法模擬查詢資料，不呼叫真實 WebService，
                '    Dim ttWPLD As New MockWPLD()
                '    ttWPLD.WPLDD = New MockWPLDD() With {
                '        .rToken = ttYLogPLD.WGetTokenParamD.rToken,
                '        .ModuleID = ttYLogPLD.WGetTokenParamD.ModuleID,
                '        .ProgID = ttYLogPLD.WGetTokenParamD.ProgID,
                '        .FuncID = ttYLogPLD.WGetTokenParamD.FuncID
                '    }
                '    ' 原本的寫法
                '    'Dim client = GetSvcYAccClient()
                '    'Dim ttWPLD As New SvcYAcc.WPLD
                '    'GetWPLDF(ttWPLD)
                '    'ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    'WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    ' 建立雛型查詢條件物件（取代 WQTBResOpenTime）
                '    Dim ttWQobj As New MockWQTBResOpenTime() With {
                '            .SemiYear = ppqmodel.SemiYear,
                '            .Semistry = ppqmodel.Semistry,
                '            .DutyOrgID = ppqmodel.DutyOrgID,
                '            .ResID = ppqmodel.ResID,
                '            .SubClassEx = ppqmodel.SubClassEx,
                '            .HourType = ppqmodel.HourType,
                '            .Week = ppqmodel.Week,
                '            .DayOfWeek = ppqmodel.DayOfWeek,
                '            .StartDate = ppqmodel.StartDate,
                '            .StopDate = ppqmodel.EndDate,
                '            .StartTime = ppqmodel.StartTime,
                '            .StopTime = ppqmodel.EndTime
                '        }

                '    ' 模擬查詢回傳資料（取代 WebService 回傳）  
                '    Dim ttWObj As New List(Of MockWTBResOpenTime) From {
                '        New MockWTBResOpenTime With {.SomeResult = "模擬時段 A"},
                '        New MockWTBResOpenTime With {.SomeResult = "模擬時段 B"}
                '    }

                '    ' 原本的寫法
                '    Dim ttWObj As New List(Of SvcYAcc.WTBResOpenTime)
                '    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime
                '    ttWQobj.SemiYear = ppqmodel.SemiYear
                '    ttWQobj.Semistry = ppqmodel.Semistry
                '    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
                '    ttWQobj.ResID = ppqmodel.ResID
                '    ttWQobj.SubClassEx = ppqmodel.SubClassEx
                '    ttWQobj.HourType = ppqmodel.HourType
                '    ttWQobj.Week = ppqmodel.Week
                '    ttWQobj.DayOfWeek = ppqmodel.DayOfWeek
                '    If Not String.IsNullOrEmpty(ppqmodel.StartDate) AndAlso ppqmodel.StartDate <> "/" Then
                '        If CInt(ppqmodel.StartDate.Replace("/", "")) < 19110000 Then
                '            ttWQobj.StartDate = CInt(ppqmodel.StartDate.Replace("/", "")) + 19110000
                '        Else
                '            ttWQobj.StopDate = ppqmodel.StartDate.Replace("/", "")
                '        End If
                '    End If
                '    If Not String.IsNullOrEmpty(ppqmodel.StartTime) Then
                '        ttWQobj.StartTime = ppqmodel.StartTime.Replace(":", "")
                '    End If
                '    If Not String.IsNullOrEmpty(ppqmodel.EndDate) AndAlso ppqmodel.EndDate <> "/" Then
                '        If CInt(ppqmodel.EndDate.Replace("/", "")) < 19110000 Then
                '            ttWQobj.StopDate = CInt(ppqmodel.EndDate.Replace("/", "")) + 19110000
                '        Else
                '            ttWQobj.StopDate = ppqmodel.EndDate.Replace("/", "")
                '        End If
                '    End If
                '    If Not String.IsNullOrEmpty(ppqmodel.EndTime) Then
                '        ttWQobj.StopTime = ppqmodel.EndTime.Replace(":", "")
                '    End If
                '    ttWQobj.BorrowerID = ppqmodel.BorrowerID


                '    'ppqmodel轉為ttWQobj
                '    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                '        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                '            For Each ttrobj In ttWObj
                '                Dim ttsobj As New YXA20Model
                '                ttsobj.objid = ttrobj.objid
                '                ttsobj.SemiYear = ttrobj.SemiYear
                '                ttsobj.Semistry = ttrobj.Semistry
                '                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                '                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                '                ttsobj.ResID = ttrobj.ResID
                '                ttsobj.ResName = ttrobj.ResName
                '                ttsobj.SubClassEx = ttrobj.SubClassEx
                '                ttsobj.HourType = ttrobj.HourType
                '                Select Case ttsobj.HourType
                '                    Case "A"
                '                        ttsobj.HourTypeStr = "整學期"
                '                    Case "B"
                '                        ttsobj.HourTypeStr = "單週"
                '                    Case "C"
                '                        ttsobj.HourTypeStr = "雙週"
                '                    Case "D"
                '                        ttsobj.HourTypeStr = "指定週"
                '                    Case "E"
                '                        ttsobj.HourTypeStr = "指定日期"
                '                End Select
                '                ttsobj.Week = ttrobj.Week
                '                ttsobj.DayOfWeek = ttrobj.DayOfWeek

                '                Dim ttST As String = ""
                '                If Not String.IsNullOrEmpty(ttrobj.StartDate) Then
                '                    If ttrobj.StartDate.Length = 8 Then
                '                        ttsobj.StartDate = CStr(CInt(Mid(ttrobj.StartDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 5, 2) + "/" + Mid(ttrobj.StartDate, 7, 2)
                '                    ElseIf ttrobj.StartDate.Length = 7 Then
                '                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 3) + "/" + Mid(ttrobj.StartDate, 4, 2) + "/" + Mid(ttrobj.StartDate, 6, 2)
                '                    ElseIf ttrobj.StartDate.Length = 6 Then
                '                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 3, 2) + "/" + Mid(ttrobj.StartDate, 5, 2)
                '                    ElseIf IsNumeric(ttrobj.StartDate) AndAlso CInt(ttrobj.StartDate) > 0 Then
                '                        ttsobj.StartDate = ttrobj.StartDate
                '                    End If
                '                End If
                '                ttsobj.StartTime = ttrobj.StartTime
                '                ttST = ttsobj.StartDate + ttsobj.StartTime

                '                Dim ttED As String = ""
                '                If Not String.IsNullOrEmpty(ttrobj.StopDate) Then
                '                    If ttrobj.StopDate.Length = 8 Then
                '                        ttsobj.StopDate = CStr(CInt(Mid(ttrobj.StopDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 5, 2) + "/" + Mid(ttrobj.StopDate, 7, 2)
                '                    ElseIf ttrobj.StopDate.Length = 7 Then
                '                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 3) + "/" + Mid(ttrobj.StopDate, 4, 2) + "/" + Mid(ttrobj.StopDate, 6, 2)
                '                    ElseIf ttrobj.StopDate.Length = 6 Then
                '                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 3, 2) + "/" + Mid(ttrobj.StopDate, 5, 2)
                '                    ElseIf IsNumeric(ttrobj.StopDate) AndAlso CInt(ttrobj.StopDate) > 0 Then
                '                        ttsobj.StopDate = ttrobj.StopDate
                '                    End If
                '                End If
                '                ttsobj.StopTime = ttrobj.StopTime
                '                ttED = ttsobj.StopDate + ttsobj.StopTime

                '                If Not String.IsNullOrEmpty(ttST) OrElse Not String.IsNullOrEmpty(ttED) Then
                '                    ttsobj.OPDTStr = ttST + "～" + ttED
                '                Else
                '                    ttsobj.OPDTStr = ""
                '                End If


                '                ttsobj.BorrowerID = ttrobj.BorrowerID
                '                Select Case ttsobj.BorrowerID
                '                    Case "0"
                '                        ttsobj.Borrower = "全體人員"
                '                    Case "1"
                '                        ttsobj.Borrower = "老師"
                '                    Case "2"
                '                        ttsobj.Borrower = "學生"
                '                    Case "3"
                '                        ttsobj.Borrower = "教職員"
                '                    Case "4"
                '                        ttsobj.Borrower = "社團"
                '                    Case "5"
                '                        ttsobj.Borrower = "校外單位"
                '                    Case "6"
                '                        ttsobj.Borrower = "計畫"
                '                End Select
                '                'ttsobj.Borrower = ttrobj.Borrower
                '                ttsobj.Hour = ttrobj.Hour
                '                ttMyList.Add(ttsobj)
                '            Next
                '        End If

                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                '    Session("ssGradeOpenL") = ttMyList
                'End If
                '結束ttMyList


                '產生Mock的ttMyList
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function





        '到新增開放時段頁面
        Public Function OpenTimeSettingsDetail(pmodel As YXA20Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Dim ttCode As New TMyCode


            Try
                Dim ssFuncID = "B2K.YXA20" '(Mock)
                If ssFuncID = "B2K.YXA20" Then
                    ViewData("FuncTitle") = "新增開放時段"
                    Dim ttMyList As New YXA20Model
                    ttMyList.objid = pmodel.objid
                    '        ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    '        If Not ttRetStr.StartsWith("OK") Then
                    '            ttErrStr.Add(ttRetStr)
                    '        Else
                    '            ViewData("SubClass") = ttCodeS
                    '        End If

                    '        'ttCodeS = New List(Of TMyCode)
                    '        'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    '        'If Not ttRetStr.StartsWith("OK") Then
                    '        '    ttErrStr.Add(ttRetStr)
                    '        'Else
                    '        '    ViewData("SubClass_2") = ttCodeS
                    '        'End If


                    Return View(ttMyList)

                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View()
        End Function


        <HttpPost>
        Public Function OpenTimeSettingsDetail_Qry(ppqmodel As YXA20Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20Model
            Dim ttResult As New JsonResult
            Try
                ttRetStr = ""

#Region "建立token"
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "OpenTimeSettingsDetail_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "DelTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }

                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                '創建ttMyList(Mock)
                ' 增加判斷，如果 objid = "-1"，則直接將 ppqmodel 指派給 ttMyList
                If ppqmodel.objid = "-1" Then
                    ttMyList = ppqmodel
                Else
                    '將mock資料從mock資料庫中找到傳給ttMyList
                    '去掉ppqmodel.objid空白的地方
                    '輸出 ppqmodel.objid 的資料到輸出視窗
                    Dim key As String = NormalizeId(If(ppqmodel?.objid, ""))
                    ttMyList = MockDataStore.Find(Function(x) x.objid = key)
                End If

                If (ttRetStr = "") Or ttRetStr.StartsWith("OK") Then
                Else
                    ttErrStr.Add(ttRetStr)
                End If
                '創建ttMyList(Reak)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    GetWPLDF(ttWPLD)
                '    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    Dim ttWobj As New List(Of SvcYAcc.WTBResOpenTime)
                '    Dim ttWQobj As New SvcYAcc.WQTBResOpenTime

                '    ttWQobj.objid = ppqmodel.objid

                '    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWobj)


                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                '        If ttWobj IsNot Nothing AndAlso ttWobj.Count > 0 Then
                '            For Each ttrobj In ttWobj
                '                Dim ttsobj As New YXA20Model
                '                ttsobj.objid = ttrobj.objid
                '                ttsobj.SemiYear = ttrobj.SemiYear
                '                ttsobj.Semistry = ttrobj.Semistry
                '                ttsobj.DutyOrgID = ttrobj.DutyOrgID
                '                ttsobj.DutyOrgName = ttrobj.DutyOrgName
                '                ttsobj.ResID = ttrobj.ResID
                '                ttsobj.ResName = ttrobj.ResName
                '                ttsobj.SubClassEx = ttrobj.SubClassEx
                '                ttsobj.HourType = ttrobj.HourType
                '                Select Case ttsobj.HourType
                '                    Case "A"
                '                        ttsobj.HourTypeStr = "整學期"
                '                    Case "B"
                '                        ttsobj.HourTypeStr = "單週"
                '                    Case "C"
                '                        ttsobj.HourTypeStr = "雙週"
                '                    Case "D"
                '                        ttsobj.HourTypeStr = "指定週"
                '                    Case "E"
                '                        ttsobj.HourTypeStr = "指定日期"
                '                End Select
                '                ttsobj.Week = ttrobj.Week
                '                ttsobj.DayOfWeek = ttrobj.DayOfWeek

                '                If Not String.IsNullOrEmpty(ttrobj.StartDate) Then
                '                    If ttrobj.StartDate.Length = 8 Then
                '                        ttsobj.StartDate = CStr(CInt(Mid(ttrobj.StartDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 5, 2) + "/" + Mid(ttrobj.StartDate, 7, 2)
                '                    ElseIf ttrobj.StartDate.Length = 7 Then
                '                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 3) + "/" + Mid(ttrobj.StartDate, 4, 2) + "/" + Mid(ttrobj.StartDate, 6, 2)
                '                    ElseIf ttrobj.StartDate.Length = 6 Then
                '                        ttsobj.StartDate = Mid(ttrobj.StartDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StartDate, 3, 2) + "/" + Mid(ttrobj.StartDate, 5, 2)
                '                    ElseIf IsNumeric(ttrobj.StartDate) AndAlso CInt(ttrobj.StartDate) > 0 Then
                '                        ttsobj.StartDate = ttrobj.StartDate
                '                    End If
                '                Else
                '                    ttsobj.StartDate = ""
                '                End If
                '                ttsobj.StartTime = ttrobj.StartTime

                '                If Not String.IsNullOrEmpty(ttrobj.StopDate) Then
                '                    If ttrobj.StopDate.Length = 8 Then
                '                        ttsobj.StopDate = CStr(CInt(Mid(ttrobj.StopDate, 1, 4)) - 1911).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 5, 2) + "/" + Mid(ttrobj.StopDate, 7, 2)
                '                    ElseIf ttrobj.StopDate.Length = 7 Then
                '                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 3) + "/" + Mid(ttrobj.StopDate, 4, 2) + "/" + Mid(ttrobj.StopDate, 6, 2)
                '                    ElseIf ttrobj.StopDate.Length = 6 Then
                '                        ttsobj.StopDate = Mid(ttrobj.StopDate, 1, 2).PadLeft(3, "0") + "/" + Mid(ttrobj.StopDate, 3, 2) + "/" + Mid(ttrobj.StopDate, 5, 2)
                '                    ElseIf IsNumeric(ttrobj.StopDate) AndAlso CInt(ttrobj.StopDate) > 0 Then
                '                        ttsobj.StopDate = ttrobj.StopDate
                '                    End If
                '                Else
                '                    ttsobj.StopDate = ""
                '                End If
                '                ttsobj.StopTime = ttrobj.StopTime

                '                ttsobj.BorrowerID = ttrobj.BorrowerID
                '                Select Case ttsobj.BorrowerID
                '                    Case "1"
                '                        ttsobj.Borrower = "老師"
                '                    Case "2"
                '                        ttsobj.Borrower = "學生"
                '                    Case "3"
                '                        ttsobj.Borrower = "教職員"
                '                    Case "4"
                '                        ttsobj.Borrower = "社團"
                '                    Case "5"
                '                        ttsobj.Borrower = "校外單位"
                '                    Case "6"
                '                        ttsobj.Borrower = "計畫"
                '                End Select
                '                'ttsobj.Borrower = ttrobj.Borrower
                '                ttsobj.Hour = ttrobj.Hour

                '                ttMyList = ttsobj
                '            Next
                '        End If
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If

                'End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function



        '儲存開放時段
        Public Function YXA20Detail_Save(ppobj As YXA20Model) As ActionResult

            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20Model
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)
            Try

                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "YXA20Detail_Save",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "SaveTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Detail_Save", "SvcYAcc", "SaveTBResOpenTime")                
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ' 1. 判斷 objid 是否為新資料
                    If String.IsNullOrEmpty(ppobj.objid) OrElse ppobj.objid = "-1" Then
                        ' 產生新的 objid（用 Max+1 寫法）
                        ' 輸出 ppobj 的資料到輸出視窗               

                        Dim newId As Integer = 1
                        If MockDataStore.Any() Then
                            Try
                                newId = MockDataStore.Max(Function(x) CInt(x.objid)) + 1
                            Catch ex As Exception
                                ' 如果轉換失敗，使用計數器方式
                                newId = MockDataStore.Count + 1
                            End Try
                        End If
                        ppobj.objid = newId.ToString()

                        MockDataStore.Add(ppobj)

                        ' 輸出 MockDataStore 的資料到輸出視窗
                        'System.Diagnostics.Debug.WriteLine("MockDataStore 內容：")
                        'System.Diagnostics.Debug.WriteLine(Newtonsoft.Json.JsonConvert.SerializeObject(MockDataStore))

                    Else
                        ' 2. 若為已存在，則覆蓋舊資料
                        Dim existIndex = MockDataStore.FindIndex(Function(x) x.objid = ppobj.objid)
                        If existIndex >= 0 Then
                            MockDataStore(existIndex) = ppobj
                        Else
                            MockDataStore.Add(ppobj) ' 若找不到則補新增
                        End If
                    End If


                    ' 3. 回傳給 ttMyList
                    ttMyList = ppobj

                End If

                '創建ttMyList(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    GetWPLDF(ttWPLD)
                '    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    Dim ttWobj As New SvcYAcc.WTBResOpenTime

                '    ttWobj.objid = ppobj.objid
                '    ttWobj.SemiYear = ppobj.SemiYear
                '    ttWobj.Semistry = ppobj.Semistry
                '    ttWobj.DutyOrgID = ppobj.DutyOrgID
                '    ttWobj.ResID = ppobj.ResID
                '    ttWobj.SubClassEx = ppobj.SubClassEx
                '    ttWobj.HourType = ppobj.HourType
                '    ttWobj.Week = ppobj.Week
                '    ttWobj.DayOfWeek = ppobj.DayOfWeek

                '    If Not String.IsNullOrEmpty(ppobj.StartDate) AndAlso ppobj.StartDate <> "/" Then
                '        If CInt(ppobj.StartDate.Replace("/", "")) < 19110000 Then
                '            ttWobj.StartDate = CInt(ppobj.StartDate.Replace("/", "")) + 19110000
                '        Else
                '            ttWobj.StartDate = ppobj.StartDate.Replace("/", "")
                '        End If
                '    End If
                '    If Not String.IsNullOrEmpty(ppobj.StartTime) Then
                '        ttWobj.StartTime = ppobj.StartTime.Replace(":", "")
                '    End If


                '    If Not String.IsNullOrEmpty(ppobj.StopDate) AndAlso ppobj.StopDate <> "/" Then
                '        If CInt(ppobj.StopDate.Replace("/", "")) < 19110000 Then
                '            ttWobj.StopDate = CInt(ppobj.StopDate.Replace("/", "")) + 19110000
                '        Else
                '            ttWobj.StopDate = ppobj.StopDate.Replace("/", "")
                '        End If
                '    End If
                '    If Not String.IsNullOrEmpty(ppobj.StopTime) Then
                '        ttWobj.StopTime = ppobj.StopTime.Replace(":", "")
                '    End If

                '    ttWobj.BorrowerID = ppobj.BorrowerID
                '    ttWobj.Hour = ppobj.Hour

                '    ttRetStr = client.SaveTBResOpenTime(ttWPLD, ttWobj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                '        If ppobj.objid = "-1" Then
                '            ttMyList.objid = ttWobj.objid
                '        Else
                '            ttMyList.objid = ppobj.objid
                '        End If
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If



                'End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20Model)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        '刪除設定好的開放時段
        <HttpPost>
        Public Function YXA20Main_Del(ppqmodel As YXA20Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "建立token"
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "YXA20Main_Del",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "DelTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Del", "SvcYAcc", "DelTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                '刪除表格欄位(Mock)
                Dim key As String = NormalizeId(If(ppqmodel?.objid, ""))
                MockDataStore.RemoveAll(Function(x) NormalizeId(x.objid) = key)

                If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                Else
                    ttErrStr.Add(ttRetStr)
                End If

                '刪除表格欄位(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient()
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    GetWPLDF(ttWPLD)
                '    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    Dim ttWObj As New SvcYAcc.WTBResOpenTime

                '    ttWObj.objid = ppqmodel.objid

                '    ttRetStr = client.DelTBResOpenTime(ttWPLD, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                'End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


#End Region


#Region "貴重儀器中心_收費標準，YXA20QModel"

        Function ChargeStandard() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of MockTMyCode)
            Try
                'Mock
                ttRetStr = "OK"
                ' 去除 title 重複的項目，只保留第一筆
                ttCodeS = Mock_F_GetCode_SubClass()
                'Real
                'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)


                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                Else
                    ViewData("SubClass") = ttCodeS
                End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function







        'Mock 收費標準設定 - 用於雛型系統測試
        <HttpPost>
        Public Function InstrumentListChargeStandar_Qry(ppqmodel As YXA20QModel) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String
            Dim ttMyList As New List(Of YXA20QModel)
            Dim ttErrStr As New List(Of String)


            Try
                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "InstrumentListChargeStandar_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Qry", "SvcYAcc", "QryTBResOpenTime")                
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GenerateMockYXA20QData(ppqmodel)
                End If


                '創建ttMyList(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    '*查資料
                '    'Dim client = GetSvcYAccClient()
                '    ' 建立查詢條件物件
                '    Dim ttWQobj As New MockWQTBResOpenTime() With {
                '    ' 回傳資料 並調整格式
                '    'ppqmodel轉為ttWQobj
                '    ttRetStr = client.QryTBResOpenTime(ttWPLD, ttWQobj, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                '                ttMyList.Add(ttsobj)
                '            Next
                '        End If

                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                '    Session("ssGradeOpenL") = ttMyList
                'End If
                '結束ttMyList

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function

        <HttpPost>
        Function ChargeStandardDetail(pmodel As YXA20QModel) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Dim ttCode As New TMyCode
            Dim ssFuncID = "B2K.YXA20" '(Mock)

            ' 設置必要的 ViewData 供 JavaScript 使用
            'ViewData("ChargeItem") = New List(Of Object) From {
            '    New With {.Code = "1", .Title = "儀器使用費"},
            '    New With {.Code = "2", .Title = "技術服務費"}
            '}

            'ViewData("ChargeMethod") = New List(Of Object) From {
            '    New With {.Code = "1", .Title = "次"},
            '    New With {.Code = "2", .Title = "天"},
            '    New With {.Code = "3", .Title = "半天"},
            '    New With {.Code = "4", .Title = "小時"}
            '}

            'ViewData("Equipment") = New List(Of Object) From {
            '    New With {.Code = "9321-25", .Title = "冷凍真空濃縮機"},
            '    New With {.Code = "9321-13", .Title = "液相層析質譜儀"},
            '    New With {.Code = "9312-1-57", .Title = "離心濃縮系統"}
            '}


            Try
                If ssFuncID = "B2K.YXA20" Then
                    ViewData("FuncTitle") = "貴重儀器中心_收費標準詳細"
                    Dim ttMyList As New YXA20QModel
                    ttMyList.objid = pmodel.objid
                    ' 輸出 MockDataStore 的資料到輸出視窗

                    'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    'If Not ttRetStr.StartsWith("OK") Then
                    '    ttErrStr.Add(ttRetStr)
                    'Else
                    '    ViewData("SubClass") = ttCodeS
                    'End If

                    'ttCodeS = New List(Of TMyCode)
                    'ttCode = New TMyCode
                    'ttCode.Code = ""
                    'ttCode.Title = "請選擇"
                    'ttCodeS.Add(ttCode)

                    'ttCode = New TMyCode
                    'ttCode.Code = "1"
                    'ttCode.Title = "場地使用費"
                    'ttCodeS.Add(ttCode)

                    'ViewData("ChargeItem") = ttCodeS

                    'ttCodeS = New List(Of TMyCode)

                    'ttCode = New TMyCode
                    'ttCode.Code = "1"
                    'ttCode.Title = "次"
                    'ttCodeS.Add(ttCode)

                    'ViewData("ChargeMethod") = ttCodeS


                    'ttCodeS = New List(Of TMyCode)

                    'ttCode = New TMyCode
                    'ttCode.Code = "1"
                    'ttCode.Title = "投影機"
                    'ttCodeS.Add(ttCode)

                    'ViewData("Equipment") = ttCodeS

                    Return View(ttMyList)

                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View(pmodel)
        End Function



        Public Function ChargeStandardDetail_Qry(ppqmodel As YXA20QModel) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String
            Dim ttMyList As New YXA20QModel
            Dim ttErrStr As New List(Of String)


            Try
                ttRetStr = ""

                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "ChargeStandardDetail_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }

                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_02Detail_Qry", "SvcYAcc", "QryTBResChargeRule")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    'ttMyList只有一個物件但是GenerateMockYXA20QData(ppqmodel)回傳的是List(Of YXA20QModel)
                    Dim tmpList As List(Of YXA20QModel) = GenerateMockYXA20QData(ppqmodel)
                    If tmpList IsNot Nothing AndAlso tmpList.Count > 0 Then
                        ttMyList = tmpList(0)
                    Else
                        ttMyList = Nothing
                    End If
                End If




            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20QModel)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20QModel)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function




        '儲存收費標準
        Public Function ChargeStandardDetail_Save(ppobj As YXA20QModel) As ActionResult

            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New YXA20QModel
            Dim ttErrStr As New List(Of String)
            Dim ttStrs As New List(Of String)
            Try

                '建立token
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "ChargeStandardDetail_Save",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "SaveTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "ChargeStandardDetail_Save", "SvcYAcc", "SaveTBResOpenTime")                
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token

                '輸出 MockDataStore 的資料到輸出視窗

                'System.Diagnostics.Debug.WriteLine(Newtonsoft.Json.JsonConvert.SerializeObject(ppobj))

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ' 1. 判斷 objid 是否為新資料
                    If String.IsNullOrEmpty(ppobj.objid) OrElse ppobj.objid = "-1" Then
                        ' 產生新的 objid（用 Max+1 寫法）
                        ' 輸出 ppobj 的資料到輸出視窗               

                        Dim newId As Integer = 1
                        If MockDataStore.Any() Then
                            Try
                                newId = MockDataStore.Max(Function(x) CInt(x.objid)) + 1
                            Catch ex As Exception
                                ' 如果轉換失敗，使用計數器方式
                                newId = MockDataStore.Count + 1
                            End Try
                        End If
                        ppobj.objid = newId.ToString()

                        MockYXA20QDataStore.Add(ppobj)

                    Else
                        ' 2. 若為已存在，則覆蓋舊資料
                        Dim existIndex = MockYXA20QDataStore.FindIndex(Function(x) x.objid = ppobj.objid)
                        If existIndex >= 0 Then
                            MockYXA20QDataStore(existIndex) = ppobj
                        Else
                            MockYXA20QDataStore.Add(ppobj) ' 若找不到則補新增
                        End If
                    End If


                    ' 3. 回傳給 ttMyList
                    ttMyList = ppobj

                End If

                '創建ttMyList(Real)，使用WCF
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient() 'New SvcYEEClient
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    Dim ttWobj As New SvcYAcc.WTBResOpenTime
                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                '        If ppobj.objid = "-1" Then
                '            ttMyList.objid = ttWobj.objid
                '        Else
                '            ttMyList.objid = ppobj.objid
                '        End If
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                'End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of YXA20QModel)
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of YXA20QModel)
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


        '刪除設定好的收費標準
        <HttpPost>
        Public Function ChargeStandard_Del(ppqmodel As YXA20QModel) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YXA20QModel)
            Dim ttErrStr As New List(Of String)

            Try
#Region "建立token"
                ' 模擬的寫法模擬後端log(Mock) 
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "ChargeStandard_Del",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "DelTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                ' 原本的寫法(Real)
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20Main_Del", "SvcYAcc", "DelTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
#End Region

                '刪除表格欄位(Mock)
                Dim key As String = NormalizeId(If(ppqmodel?.objid, ""))
                MockYXA20QDataStore.RemoveAll(Function(x) NormalizeId(x.objid) = key)

                If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                Else
                    ttErrStr.Add(ttRetStr)
                End If
                '刪除表格欄位(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient()
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    GetWPLDF(ttWPLD)
                '    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    Dim ttWObj As New SvcYAcc.WTBResOpenTime

                '    ttWObj.objid = ppqmodel.objid

                '    ttRetStr = client.DelTBResOpenTime(ttWPLD, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If
                'End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YXA20QModel))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function






#End Region



#Region "貴重儀器中心_YIC20_03_設備租借審核"


        '首頁
        Function YIC20_03() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "設備租借審核"
                'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                '    If Not ttRetStr.StartsWith("OK") Then
                '        ttErrStr.Add(ttRetStr)
                '    Else
                '        ViewData("SubClass") = ttCodeS
                '    End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function



        '查詢YXA20_03Model
        <HttpPost>
        Public Function YIC20_03_Qry(ppqmodel As YIC20_03_Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YIC20_03_Model)
            Dim ttErrStr As New List(Of String)

            Try

#Region "token"
                'Mock建立token
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "YIC20_03Main_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                '原本寫法(Real)
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YIC20_03Main_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                '結束token  
#End Region

                '創建ttMyList(Mock)
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GetMockYIC20_03_APLData(ppqmodel.Core, ppqmodel.ResID)
                End If

                '原本寫法(Real)
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient()
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD
                '    GetWPLDF(ttWPLD)
                '    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
                '    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

                '    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
                '    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

                '    'ttWQobj.DutyOrgID = ppqmodel.Borrower
                '    'ttWQobj.DutyOrgID = ppqmodel.IsPass

                '    'ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

                '    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

                '        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
                '            For Each ttrobj In ttWObj
                '                Dim ttsobj As New YXA20_03Model
                '                'ttsobj.objid = ttrobj.objid
                '                'ttsobj.DutyOrgID = ttrobj.DutyOrgID
                '                'ttsobj.DutyOrgName = ttrobj.DutyOrgName
                '                'ttsobj.ResID = ttrobj.ResID
                '                'ttsobj.ResName = ttrobj.ResName
                '                'ttsobj.SubClassEx = ttrobj.SubClassEx

                '                'ttsobj.BorrowerID = ttrobj.BorrowerID
                '                'Select Case ttsobj.BorrowerID
                '                '    Case "0"
                '                '        ttsobj.Borrower = "全體人員"
                '                '    Case "1"
                '                '        ttsobj.Borrower = "老師"
                '                '    Case "2"
                '                '        ttsobj.Borrower = "學生"
                '                '    Case "3"
                '                '        ttsobj.Borrower = "教職員"
                '                '    Case "4"
                '                '        ttsobj.Borrower = "社團"
                '                '    Case "5"
                '                '        ttsobj.Borrower = "校外單位"
                '                '    Case "6"
                '                '        ttsobj.Borrower = "計畫"
                '                'End Select

                '                'ttsobj.ChargeItem = ttrobj.ChargeItem
                '                'Select Case ttsobj.ChargeItem
                '                '    Case "1"
                '                '        ttsobj.ChargeItemStr = "場地使用費"
                '                '    Case "2"
                '                '        ttsobj.ChargeItemStr = "設備使用費"
                '                '    Case "3"
                '                '        ttsobj.ChargeItemStr = "清潔費"
                '                'End Select

                '                'ttsobj.TimeClass = ttrobj.TimeClass
                '                'Select Case ttsobj.TimeClass
                '                '    Case "0"
                '                '        ttsobj.TimeClassStr = "全天"
                '                '    Case "1"
                '                '        ttsobj.TimeClassStr = "日"
                '                '    Case "2"
                '                '        ttsobj.TimeClassStr = "夜"
                '                '    Case "3"
                '                '        ttsobj.TimeClassStr = "假日"
                '                'End Select

                '                'ttsobj.ChargeMethod = ttrobj.ChargeMethod
                '                'Select Case ttsobj.ChargeMethod
                '                '    Case "1"
                '                '        ttsobj.ChargeMethodStr = "次"
                '                '    Case "2"
                '                '        ttsobj.ChargeMethodStr = "天"
                '                '    Case "3"
                '                '        ttsobj.ChargeMethodStr = "半天"
                '                '    Case "4"
                '                '        ttsobj.ChargeMethodStr = "小時"
                '                '    Case "5"
                '                '        ttsobj.ChargeMethodStr = "半小時"
                '                '    Case "6"
                '                '        ttsobj.ChargeMethodStr = "分鐘"
                '                'End Select

                '                'ttsobj.Price = ttrobj.Price
                '                'ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
                '                'ttsobj.LateFee = ttrobj.LateFee
                '                'ttsobj.Management = ttrobj.Management
                '                'ttsobj.Deposit = ttrobj.Deposit
                '                'ttsobj.Descript = ttrobj.Descript

                '                ttMyList.Add(ttsobj)
                '            Next
                '        End If



                '        Dim ttObj As New YXA20_03Model
                '        ttObj.objid = "1"
                '        ttObj.OpDTobjid = "5"
                '        ttObj.DutyOrgID = "A506"
                '        ttObj.DutyOrgName = "教務處"
                '        ttObj.ResID = "0002"
                '        ttObj.ResName = "2教室"
                '        ttObj.SubClassEx = ""
                '        ttObj.BorrowerName = "測試學生"
                '        ttObj.OPDTStr = "114/07/22 09:00 ~ 114/07/22 12:00"
                '        ttObj.Status = "20"
                '        ttObj.StatusText = "審核中"
                '        'ttObj.Hour = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
                '        ttMyList.Add(ttObj)
                '    Else
                '        ttErrStr.Add(ttRetStr)
                '    End If

                'End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YIC20_03_Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YIC20_03_Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


        '場地查詢進入頁面
        Function YIC20_03Res() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "設備租借查詢"
                'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                'If Not ttRetStr.StartsWith("OK") Then
                '    ttErrStr.Add(ttRetStr)
                'Else
                '    ViewData("SubClass") = ttCodeS
                'End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        '可以使用設備查詢
        <HttpPost>
        Public Function YIC20_03Res_Qry(ppqmodel As YIC20_03ResQ) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YIC20_03ResModel)
            Dim ttErrStr As New List(Of String)

            Try
                'Mock建立token
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "YIC20_03Res_Qry",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA20_03Res_Qry", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                'Mock
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GetMockYIC20_03ResModel(ppqmodel.Core, ppqmodel.ResID, ppqmodel.ResName)
                Else
                    ttErrStr.Add(ttRetStr)
                End If
                Session("ssGradeOpenL") = ttMyList

                'Real
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient()
                '    '*查資料
                '    Dim ttWPLD As New SvcYAcc.WPLD


            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YIC20_03ResModel))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YIC20_03ResModel))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function


        '申請單詳細
        Function YIC20_03Detail(pmodel As YIC20_03Model) As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Dim ssFuncID As String = "B2K.YIC20" 'Mock
            Try
                If ssFuncID = "B2K.YIC20" Then
                    ViewData("FuncTitle") = "申請單"
                    Dim ttMyList As New YIC20_03Model
                    ttMyList.objid = pmodel.objid
                    ttMyList.OpDTobjid = pmodel.OpDTobjid

                    'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                    'If Not ttRetStr.StartsWith("OK") Then
                    '    ttErrStr.Add(ttRetStr)
                    'Else
                    '    ViewData("SubClass") = ttCodeS
                    'End If

                    Return View(ttMyList)
                Else
                    Return Json(String.Join(vbCrLf, "查無頁面!"), JsonRequestBehavior.AllowGet) 'View("../Shared/Error")
                End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Return View()
        End Function



        '        <HttpPost>
        '        Public Function YXA20_03Detail_Qry(ppqmodel As YXA20_03Model) As JsonResult
        '            Dim ttResult As New JsonResult
        '            Dim ttRetStr As String = ""
        '            Dim ttMyList As New YXA20_03Model
        '            Dim ttErrStr As New List(Of String)

        '            Try

        '#Region "token"
        '                Dim ttsvcLog = GetSvcYLogClient()
        '                Dim ttYLogPLD As New SvcYLog.WGetTokenParam
        '                ttYLogPLD.WGetTokenParamF = ssYLogPLDF
        '                ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
        '                SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YXA30Detail_Qry", "SvcYAcc", "QryTBResChargeRule")
        '                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
        '                If Not ttRetStr.StartsWith("OK") Then
        '                    ttErrStr.Add(ttRetStr)
        '                End If
        '#End Region

        '                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
        '                    Dim client = GetSvcYAccClient()
        '                    '*查資料
        '                    Dim ttWPLD As New SvcYAcc.WPLD
        '                    GetWPLDF(ttWPLD)
        '                    ttWPLD.WPLDD = New SvcYAcc.WPLDD 'Dim ttYEEPLDD As New SvcYEE.WPLDD
        '                    WYPLDCopyFrom(ttWPLD.WPLDD, ttYLogPLD.WGetTokenParamD)

        '                    Dim ttWObj As New List(Of SvcYAcc.WTBResChargeRule)
        '                    Dim ttWQobj As New SvcYAcc.WQTBResChargeRule

        '                    ttWQobj.DutyOrgID = ppqmodel.DutyOrgID
        '                    ttWQobj.ResID = ppqmodel.ResID
        '                    ttWQobj.SubClassEx = ppqmodel.SubClassEx
        '                    ttWQobj.BorrowerID = ppqmodel.BorrowerID

        '                    'ttRetStr = client.QryTBResChargeRule(ttWPLD, ttWQobj, ttWObj)

        '                    If (ttRetStr = "") Or (ttRetStr.StartsWith("OK")) Then

        '                        If ttWObj IsNot Nothing AndAlso ttWObj.Count > 0 Then
        '                            For Each ttrobj In ttWObj
        '                                Dim ttsobj As New YXA20_03Model
        '                                'ttsobj.objid = ttrobj.objid
        '                                'ttsobj.DutyOrgID = ttrobj.DutyOrgID
        '                                'ttsobj.DutyOrgName = ttrobj.DutyOrgName
        '                                'ttsobj.ResID = ttrobj.ResID
        '                                'ttsobj.ResName = ttrobj.ResName
        '                                'ttsobj.SubClassEx = ttrobj.SubClassEx

        '                                'ttsobj.BorrowerID = ttrobj.BorrowerID
        '                                'Select Case ttsobj.BorrowerID
        '                                '    Case "0"
        '                                '        ttsobj.Borrower = "全體人員"
        '                                '    Case "1"
        '                                '        ttsobj.Borrower = "老師"
        '                                '    Case "2"
        '                                '        ttsobj.Borrower = "學生"
        '                                '    Case "3"
        '                                '        ttsobj.Borrower = "教職員"
        '                                '    Case "4"
        '                                '        ttsobj.Borrower = "社團"
        '                                '    Case "5"
        '                                '        ttsobj.Borrower = "校外單位"
        '                                '    Case "6"
        '                                '        ttsobj.Borrower = "計畫"
        '                                'End Select

        '                                'ttsobj.ChargeItem = ttrobj.ChargeItem
        '                                'Select Case ttsobj.ChargeItem
        '                                '    Case "1"
        '                                '        ttsobj.ChargeItemStr = "場地使用費"
        '                                '    Case "2"
        '                                '        ttsobj.ChargeItemStr = "設備使用費"
        '                                '    Case "3"
        '                                '        ttsobj.ChargeItemStr = "清潔費"
        '                                'End Select

        '                                'ttsobj.TimeClass = ttrobj.TimeClass
        '                                'Select Case ttsobj.TimeClass
        '                                '    Case "0"
        '                                '        ttsobj.TimeClassStr = "全天"
        '                                '    Case "1"
        '                                '        ttsobj.TimeClassStr = "日"
        '                                '    Case "2"
        '                                '        ttsobj.TimeClassStr = "夜"
        '                                '    Case "3"
        '                                '        ttsobj.TimeClassStr = "假日"
        '                                'End Select

        '                                'ttsobj.ChargeMethod = ttrobj.ChargeMethod
        '                                'Select Case ttsobj.ChargeMethod
        '                                '    Case "1"
        '                                '        ttsobj.ChargeMethodStr = "次"
        '                                '    Case "2"
        '                                '        ttsobj.ChargeMethodStr = "天"
        '                                '    Case "3"
        '                                '        ttsobj.ChargeMethodStr = "半天"
        '                                '    Case "4"
        '                                '        ttsobj.ChargeMethodStr = "小時"
        '                                '    Case "5"
        '                                '        ttsobj.ChargeMethodStr = "半小時"
        '                                '    Case "6"
        '                                '        ttsobj.ChargeMethodStr = "分鐘"
        '                                'End Select

        '                                'ttsobj.Price = ttrobj.Price
        '                                'ttsobj.LateFeeUnit = ttrobj.LateFeeUnit
        '                                'ttsobj.LateFee = ttrobj.LateFee
        '                                'ttsobj.Management = ttrobj.Management
        '                                'ttsobj.Deposit = ttrobj.Deposit
        '                                'ttsobj.Descript = ttrobj.Descript

        '                                ttMyList = ttsobj
        '                            Next
        '                        End If



        '                        Dim ttObj As New YXA20_03Model
        '                        ttObj.objid = "1"
        '                        ttObj.DutyOrgID = "A506"
        '                        'ttsobj.DutyOrgName = ttrobj.DutyOrgName
        '                        ttObj.ResID = "0002"
        '                        'ttsobj.ResName = ttrobj.ResName
        '                        ttObj.SubClassEx = ""
        '                        ttObj.BorrowerID = "2"
        '                        ttObj.BorrowerName = "測試學生"
        '                        ttObj.Phone = "0900000000"
        '                        ttObj.Descript = "備註測試"
        '                        ttObj.Status = "90"
        '                        'ttObj.Hour = "YYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
        '                        ttMyList = ttObj

        '                    Else
        '                        ttErrStr.Add(ttRetStr)
        '                    End If


        '                    Session("ssGradeOpenL") = ttMyList
        '                End If

        '            Catch ex As Exception
        '                ttErrStr.Add(ex.Message)
        '            End Try
        '            If ttErrStr.Count > 0 Then
        '                Dim obj As New myResult(Of YXA20_03Model)
        '                obj.OK = False
        '                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
        '                obj.obj = ttMyList
        '                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
        '            Else
        '                Dim obj As New myResult(Of YXA20_03Model)
        '                obj.OK = True
        '                obj.MSG = ""
        '                obj.obj = ttMyList
        '                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
        '            End If

        '            Return ttResult
        '        End Function




        ''' <summary>
        ''' 查詢開放時間
        ''' </summary>
        <HttpPost>
        Public Function YIC20_03Detail_QryOpDT(ppqmodel As YIC20_03Model) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttRetStr As String = ""
            Dim ttMyList As New List(Of YIC20_03Model)
            Dim ttErrStr As New List(Of String)

            Try

                'token
                'Mock
                Dim ttsvcLog = New MockSvcYLogClient()                  ' 
                Dim ttYLogPLD As New SvcYLog.WGetTokenParam With {
                    .WGetTokenParamF = New SvcYLog.WGetTokenParamF(),
                    .WGetTokenParamD = New SvcYLog.WGetTokenParamD With {
                        .Method = "YIC20_03Detail_QryOpDT",
                        .WCFName = "SvcYAcc",
                        .WCFMethod = "QryTBResOpenTime",
                        .rToken = "DummyToken123"
                    }
                }
                'Dim ttsvcLog = GetSvcYLogClient()
                'Dim ttYLogPLD As New SvcYLog.WGetTokenParam
                'ttYLogPLD.WGetTokenParamF = ssYLogPLDF
                'ttYLogPLD.WGetTokenParamD = New SvcYLog.WGetTokenParamD
                'SetWYLogPLDD(ttYLogPLD.WGetTokenParamD, "YIC20_03Detail_QryOpDT", "SvcYAcc", "QryTBResOpenTime")
                ttRetStr = ttsvcLog.GetToken(ttYLogPLD)
                If Not ttRetStr.StartsWith("OK") Then
                    ttErrStr.Add(ttRetStr)
                End If
                'end token

                'Mock
                If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                    ttMyList = GetMockYIC20_03Model(ppqmodel.objid, ppqmodel.OpDTobjid)
                Else
                    ttErrStr.Add(ttRetStr)
                End If

                'Real
                'If ttErrStr.Count = 0 AndAlso Not String.IsNullOrEmpty(ttYLogPLD.WGetTokenParamD.rToken) Then
                '    Dim client = GetSvcYAccClient()
                '    '*查資料                   
                'End If
                'End If

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            If ttErrStr.Count > 0 Then
                Dim obj As New myResult(Of List(Of YIC20_03Model))
                obj.OK = False
                obj.MSG = "Qry-X-" + String.Join(vbLf, ttErrStr) 'WinG 2022.10.03 + "Qry-X-"
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Else
                Dim obj As New myResult(Of List(Of YIC20_03Model))
                obj.OK = True
                obj.MSG = ""
                obj.obj = ttMyList
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End If

            Return ttResult
        End Function








        '歸還
        Function YIC20_03Return(pmodel As YIC20_03Return) As ActionResult

            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Dim ttMyList As New YIC20_03Return
            Dim ssFuncID As String = "B2K.YIC20" 'Mock

            '--- 撈申請單資料（ApplyNo） ---
            'Dim applyList As List(Of YXA20_03_Model) = Nothing
            Try

                ViewData("FuncTitle") = "場地歸還"

                ' 設定 Model 的預設值，避免編譯錯誤
                ttMyList.objid = If(pmodel?.objid, "")
                ttMyList.ResID = If(pmodel?.ResID, "")
                ttMyList.ResName = If(pmodel?.ResName, "多功能掃描電子顯微鏡") ' 提供預設值
                ttMyList.BorrowerName = If(pmodel?.BorrowerName, "")
                ttMyList.Phone = If(pmodel?.Phone, "")
                
                ' 設定其他必要的預設值
                ttMyList.Management = ""
                ttMyList.Price = ""
                ttMyList.Deposit = ""
                ttMyList.DepositRe = ""
                ttMyList.ActualPrice = ""
                ttMyList.Descript = ""
                ttMyList.ReturnTimeL = New List(Of YIC20_03ReTime)
                
                Console.WriteLine("ttMyList.objid: " + ttMyList.objid)
                Console.WriteLine("ttMyList.ResID: " + ttMyList.ResID)
                Console.WriteLine("ttMyList.ResName: " + ttMyList.ResName)
                Console.WriteLine("ttMyList.BorrowerName: " + ttMyList.BorrowerName)
                
                Return View(ttMyList)



                'Dim allApplyData As List(Of YXA20_03_Model) = TryCast(Session("ssApplyData"), List(Of YXA20_03_Model))
                'applyList = allApplyData.Where(Function(x) ppApplyNoList.Contains(x.ApplyNo)).ToList()
                'If applyList IsNot Nothing AndAlso applyList.Any() Then
                '    ViewData("ApplyList") = applyList
                'End If
            Catch ex As Exception
                ' 發生錯誤時也要傳回有效的 Model
                ttMyList.ResName = "設備"
                Return View(ttMyList)
            End Try
        End Function


#End Region



#Region "貴重儀器中心_YIC20_04_人員管理"


        '首頁
        Function YIC20_04() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)

            Try
                ViewData("FuncTitle") = "人員管理"
                'ttRetStr = F_GetCode_SubClass("1300100", ttCodeS)
                '    If Not ttRetStr.StartsWith("OK") Then
                '        ttErrStr.Add(ttRetStr)
                '    Else
                '        ViewData("SubClass") = ttCodeS
                '    End If
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function

        ' 查詢
        <HttpPost>
        Public Function YIC20_04_Qry() As JsonResult
            Dim ttResult As New JsonResult
            Dim ttErrStr As New List(Of String)


            Return ttResult
        End Function



        ' 校內申請
        Function YIC20_04Detail_SchoolApply() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Try
                ViewData("SubFuncTitle") = "校內申請"
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function




        ' 學研機構申請
        Function YIC20_04Detail_ResearchApply() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Try
                ViewData("FuncTitle") = "學研機構申請"
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()


        End Function

        ' 廠商申請
        Function YIC20_04Detail_CompanyApply() As ActionResult
            Dim ttErrStr As New List(Of String)
            Dim ttRetStr As String = ""
            Dim ttCodeS As New List(Of TMyCode)
            Try
                ViewData("FuncTitle") = "廠商申請"
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try
            Return View()
        End Function





#End Region


#Region "日曆calendar"

        Function Callendar() As ActionResult
            ViewData("FuncTitle") = "貴重儀器中心_日曆"
            ' 傳送 calendar mock data 到視圖
            ViewData("CalendarEvents") = calendar
            Return View()
        End Function

        ' API 端點：取得行事曆事件資料（核心聚合模式）
        <HttpPost>
        Public Function GetCalendarEvents() As JsonResult
            Dim ttResult As New JsonResult
            Dim ttErrStr As New List(Of String)

            Try
                ' 測試：建立聚合事件 - 版本測試
                Dim events As New List(Of Object)
                
                ' 添加測試事件確認代碼生效
                events.Add(New With {
                    .title = "測試-代碼已更新",
                    .start = "2025-08-15T10:00:00",
                    .end = "2025-08-15T11:00:00",
                    .color = "#00ff00",
                    .eventType = "test"
                })
                
                ' 改進的聚合邏輯：處理跨天事件和多日期
                Dim coreByDateGroups As New Dictionary(Of String, Dictionary(Of String, List(Of YIC20_03Calendar)))

                ' 為每個事件生成所有涉及的日期
                For Each item In calendar
                    Dim startDate As DateTime = DateTime.ParseExact(item.StartDate, "yyyy/MM/dd", Nothing)
                    Dim endDate As DateTime = DateTime.ParseExact(item.StopDate, "yyyy/MM/dd", Nothing)

                    ' 為每個涉及的日期創建條目
                    Dim currentDate As DateTime = startDate
                    While currentDate <= endDate
                        Dim dateKey As String = currentDate.ToString("yyyy/MM/dd")

                        If Not coreByDateGroups.ContainsKey(dateKey) Then
                            coreByDateGroups(dateKey) = New Dictionary(Of String, List(Of YIC20_03Calendar))
                        End If

                        If Not coreByDateGroups(dateKey).ContainsKey(item.Core) Then
                            coreByDateGroups(dateKey)(item.Core) = New List(Of YIC20_03Calendar)
                        End If

                        coreByDateGroups(dateKey)(item.Core).Add(item)
                        currentDate = currentDate.AddDays(1)
                    End While
                Next

                ' 為每個日期的每個核心創建聚合事件
                For Each dateKvp In coreByDateGroups
                    Dim eventDate = dateKvp.Key
                    Dim coreGroups = dateKvp.Value

                    For Each coreKvp In coreGroups
                        Dim core = coreKvp.Key
                        Dim coreEvents = coreKvp.Value.Distinct().ToList() ' 去重，避免同一設備在同一天重複計算
                        Dim equipmentCount = coreEvents.Count

                        ' 根據核心類型設定不同顏色
                        Dim backgroundColor As String = "#3788d8" ' 預設藍色
                        Select Case core
                            Case "蛋白質體學核心"
                                backgroundColor = "#ff6b6b" ' 紅色
                            Case "基因體學核心"
                                backgroundColor = "#4ecdc4" ' 青色
                            Case "細胞分析核心"
                                backgroundColor = "#45b7d1" ' 藍色
                            Case "顯微影像核心"
                                backgroundColor = "#f7b731" ' 橘黃色
                            Case "小動物影像核心"
                                backgroundColor = "#a55eea" ' 紫色
                            Case "分析軟體"
                                backgroundColor = "#20bf6b" ' 綠色
                            Case "共用/Demo/外借儀器"
                                backgroundColor = "#778ca3" ' 灰藍色
                        End Select

                        ' 建立聚合事件（全天事件，不顯示時間）
                        events.Add(New With {
                            .title = core + " (" + equipmentCount.ToString() + "台設備)",
                            .start = eventDate.Replace("/", "-"), ' 全天事件格式：只有日期
                            .allDay = True, ' 標記為全天事件
                            .color = backgroundColor,
                            .core = core,
                            .equipmentCount = equipmentCount,
                            .date = eventDate,
                            .eventType = "coreAggregate" ' 標記這是聚合事件
                        })
                    Next
                Next

                Dim obj As New myResult(Of List(Of Object))
                obj.OK = True
                obj.MSG = ""
                obj.obj = events
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
                Dim obj As New myResult(Of List(Of Object))
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = New List(Of Object)
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End Try

            Return ttResult
        End Function

        ' API 端點：取得特定日期特定核心的詳細事件資料
        <HttpPost>
        Public Function GetCoreDetailEvents(coreType As String, eventDate As String) As JsonResult
            Dim ttResult As New JsonResult
            Dim ttErrStr As New List(Of String)

            Try
                Dim events As New List(Of Object)
                
                ' 篩選特定日期和核心的事件
                Dim filteredEvents = calendar.Where(Function(item) _
                    item.Core = coreType AndAlso item.StartDate = eventDate.Replace("-", "/") _
                ).ToList()

                For Each item In filteredEvents
                    ' 計算事件的開始和結束時間，將日期格式從 YYYY/MM/DD 轉為 YYYY-MM-DD
                    Dim startDateTime As String = item.StartDate.Replace("/", "-") + "T" + item.StartTime + ":00"
                    Dim endDateTime As String = item.StopDate.Replace("/", "-") + "T" + item.StopTime + ":00"

                    ' 根據核心類型設定不同顏色
                    Dim backgroundColor As String = "#3788d8" ' 預設藍色
                    Select Case item.Core
                        Case "蛋白質體學核心"
                            backgroundColor = "#ff6b6b" ' 紅色
                        Case "基因體學核心"
                            backgroundColor = "#4ecdc4" ' 青色
                        Case "細胞分析核心"
                            backgroundColor = "#45b7d1" ' 藍色
                        Case "顯微影像核心"
                            backgroundColor = "#f7b731" ' 橘黃色
                        Case "小動物影像核心"
                            backgroundColor = "#a55eea" ' 紫色
                        Case "分析軟體"
                            backgroundColor = "#20bf6b" ' 綠色
                        Case "共用/Demo/外借儀器"
                            backgroundColor = "#778ca3" ' 灰藍色
                    End Select

                    events.Add(New With {
                        .title = item.ResName + " - " + item.BorrowerName,
                        .start = startDateTime,
                        .end = endDateTime,
                        .color = backgroundColor,
                        .core = item.Core,
                        .resName = item.ResName,
                        .borrowerName = item.BorrowerName,
                        .startTime = item.StartTime,
                        .stopTime = item.StopTime,
                        .eventType = "equipmentDetail" ' 標記這是詳細設備事件
                    })
                Next

                Dim obj As New myResult(Of List(Of Object))
                obj.OK = True
                obj.MSG = ""
                obj.obj = events
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)

            Catch ex As Exception
                ttErrStr.Add(ex.Message)
                Dim obj As New myResult(Of List(Of Object))
                obj.OK = False
                obj.MSG = String.Join(vbLf, ttErrStr)
                obj.obj = New List(Of Object)
                ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            End Try

            Return ttResult
        End Function

        'Public Shared calendar As New List(Of YIC20_03Calendar) From {
        ' 修正：calendar 改為 Friend，避免 BC30909 錯誤（class YIC20_03Calendar 不是 public）
        Friend Shared calendar As New List(Of YIC20_03Calendar) From {
            New YIC20_03Calendar With {
                .Core = "蛋白質體學核心",
                .ResName = "多功能掃描電子顯微鏡",
                .BorrowerName = "李四",
                .StartDate = "2025/08/15",
                .StartTime = "09:00",
                .StopDate = "2025/08/16",
                .StopTime = "12:00"
            },
            New YIC20_03Calendar With {
                .Core = "基因體學核心",
                .ResName = "基因分析系統",
                .BorrowerName = "王五",
                .StartDate = "2025/08/15",
                .StartTime = "09:00",
                .StopDate = "2025/08/16",
                .StopTime = "12:00"
            },
            New YIC20_03Calendar With {
                .Core = "細胞分析核心",
                .ResName = "細胞分析儀",
                .BorrowerName = "王小明",
                .StartDate = "2025/08/15",
                .StartTime = "09:00",
                .StopDate = "2025/08/16",
                .StopTime = "12:00"
            },
            New YIC20_03Calendar With {
                .Core = "細胞分析核心",
                .ResName = "細胞分析儀",
                .BorrowerName = "張三",
                .StartDate = "2025/08/15",
                .StartTime = "09:00",
                .StopDate = "2025/08/17",
                .StopTime = "12:00"
            }
        }

#End Region



#Region "Mock資料放置區"

        '************共用工具區**************
        ' 統一處理移除所有空白、轉大寫的小工具：
        Private Function NormalizeId(s As String) As String
            If s Is Nothing Then Return ""
            ' 去除所有空白（含半形/全形/Tab/換行）
            Dim noSpaces = New String(s.Where(Function(ch) Not Char.IsWhiteSpace(ch)).ToArray())
            Return noSpaces.Trim().ToUpperInvariant()
        End Function

        ' Mock_F_GetCode_SubClass
        ' 取得去除 title 重複的項目，只保留第一筆
        Private Function Mock_F_GetCode_SubClass() As List(Of MockTMyCode)
            Return MockDataProvider.MockGetICenterCore().
                GroupBy(Function(x) x.Title).
                Select(Function(g) g.First()).
                ToList()
        End Function


        'Mock_F_GetCode_EquQry，
        Public Function Mock_F_GetCode_EquQry(ppqmodel As YXA20QModel) As JsonResult
            ' 以ppqmodel.Core（例如"蛋白質體學核心"）為定值，找出MockTMyCode.title等於該值的所有param0
            Dim selectedCore As String = ppqmodel.Core
            Dim allCores As List(Of MockTMyCode) = MockDataProvider.MockGetICenterCore()
            Dim ttMyList As New List(Of TMyCode)
            Dim ttErrStr As New List(Of String)
            Dim ttResult As JsonResult


            Try
                ' 將符合條件的 param0 轉為 TMyCode 並加入 ttMyList
                ' 改為去除 param0 重複

                Dim matchedCores = allCores.Where(Function(x) x.Title = selectedCore).GroupBy(Function(x) x.param0).Select(Function(g) g.First()).ToList()
                If matchedCores IsNot Nothing AndAlso matchedCores.Count > 0 Then
                    For Each ttrobj In matchedCores
                        Dim ttobj As New TMyCode
                        ttobj.Code = ttrobj.param0
                        ttobj.Title = ttrobj.param0
                        ttMyList.Add(ttobj)
                    Next
                End If
                Session("ssGradeOpenList") = ttMyList
            Catch ex As Exception
                ttErrStr.Add(ex.Message)
            End Try

            Dim obj As New myResult(Of List(Of TMyCode))
            obj.OK = True
            obj.MSG = ""
            obj.obj = ttMyList
            ttResult = Json(obj, JsonRequestBehavior.AllowGet)
            Return ttResult
        End Function




        '************Mock YXA20Model**************
        ' 全域記憶體模擬資料庫（以 objid 當作唯一識別），開放時間設定
        Public Shared Property MockDataStore As New List(Of YXA20Model) From {
            New YXA20Model With {
                .objid = "001",
                .DutyOrgID = "這是mock",
                .DutyOrgName = "這是mock",
                .ResID = "9321-25",
                .ResName = "高解析度掃描電鏡",
                .HourType = "A",
                .HourTypeStr = "整學期",
                .Week = "",
                .DayOfWeek = "1,2,3,4,5",
                .OPDTStr = "112/09/01 08:00～112/12/31 17:00",
                .BorrowerID = "0",
                .Borrower = "全體人員",
                .Hour = "Y"
            },
            New YXA20Model With {
                .objid = "002",
                .DutyOrgID = "貴重儀器中心",
                .DutyOrgName = "貴重儀器中心",
                .ResID = "9321-25",
                .ResName = "X光繞射儀",
                .HourType = "D",
                .HourTypeStr = "指定週",
                .Week = "1,3,5,7,9",
                .DayOfWeek = "1,2,3,4,5",
                .OPDTStr = "112/09/01 09:00～112/12/31 16:00",
                .BorrowerID = "2",
                .Borrower = "學生",
                .Hour = "YY"
            },
            New YXA20Model With {
                .objid = "003",
                .DutyOrgID = "其他",
                .DutyOrgName = "其他單位",
                .ResID = "9321-13",
                .ResName = "核磁共振儀",
                .HourType = "E",
                .HourTypeStr = "指定日期",
                .Week = "1,3",
                .DayOfWeek = "1,3",
                .OPDTStr = "112/10/15 10:00～112/10/15 15:00",
                .BorrowerID = "3",
                .Borrower = "教職員",
                .Hour = "YYY"
            }
        }

        '根據查詢條件篩選 MockDataStore，回傳符合條件的 YXA20Model 清單
        ' <param name="queryModel">查詢條件</param>
        ' <returns>符合條件的 YXA20Model 清單</returns>
        Public Shared Function GenerateMockYXA20Data(queryModel As YXA20Model) As List(Of YXA20Model)
            Dim result As New List(Of YXA20Model)

            For Each item In MockDataStore
                Dim shouldInclude As Boolean = True

                ' 權責單位過濾
                If Not String.IsNullOrEmpty(queryModel.DutyOrgID) AndAlso
                   queryModel.DutyOrgID <> item.DutyOrgID Then
                    shouldInclude = False
                End If

                ' 週別過濾
                If Not String.IsNullOrEmpty(queryModel.HourType) AndAlso
                   queryModel.HourType <> item.HourType Then
                    shouldInclude = False
                End If

                ' 開放對象過濾
                If Not String.IsNullOrEmpty(queryModel.BorrowerID) AndAlso
                   queryModel.BorrowerID <> item.BorrowerID Then
                    shouldInclude = False
                End If

                ' 儀器編號過濾
                If Not String.IsNullOrEmpty(queryModel.ResID) AndAlso
                   queryModel.ResID <> item.ResID Then
                    shouldInclude = False
                End If

                ' 分類過濾
                If Not String.IsNullOrEmpty(queryModel.SubClassEx) AndAlso
                   (item.SubClassEx Is Nothing OrElse queryModel.SubClassEx <> item.SubClassEx) Then
                    shouldInclude = False
                End If

                ' 週次過濾
                If Not String.IsNullOrEmpty(queryModel.Week) AndAlso
                   (item.Week Is Nothing OrElse Not item.Week.Split(","c).Contains(queryModel.Week)) Then
                    shouldInclude = False
                End If

                ' 星期過濾
                If Not String.IsNullOrEmpty(queryModel.DayOfWeek) AndAlso
                   (item.DayOfWeek Is Nothing OrElse Not item.DayOfWeek.Split(","c).Contains(queryModel.DayOfWeek)) Then
                    shouldInclude = False
                End If

                ' 開始日期過濾（假設 OPDTStr 格式為 "yyyy/MM/dd HH:mm～yyyy/MM/dd HH:mm"）
                If Not String.IsNullOrEmpty(queryModel.StartDate) Then
                    Dim startDate As DateTime
                    If DateTime.TryParse(queryModel.StartDate, startDate) Then
                        Dim opdtStartStr As String = ""
                        If Not String.IsNullOrEmpty(item.OPDTStr) AndAlso item.OPDTStr.Contains("～") Then
                            opdtStartStr = item.OPDTStr.Split("～"c)(0).Trim().Split(" "c)(0)
                        End If
                        Dim opdtStartDate As DateTime
                        If DateTime.TryParse(opdtStartStr, opdtStartDate) Then
                            If startDate < opdtStartDate Then
                                shouldInclude = False
                            End If
                        End If
                    End If
                End If

                ' 結束日期過濾
                If Not String.IsNullOrEmpty(queryModel.StopDate) Then
                    Dim stopDate As DateTime
                    If DateTime.TryParse(queryModel.StopDate, stopDate) Then
                        Dim opdtEndStr As String = ""
                        If Not String.IsNullOrEmpty(item.OPDTStr) AndAlso item.OPDTStr.Contains("～") Then
                            opdtEndStr = item.OPDTStr.Split("～"c)(1).Trim().Split(" "c)(0)
                        End If
                        Dim opdtEndDate As DateTime
                        If DateTime.TryParse(opdtEndStr, opdtEndDate) Then
                            If stopDate > opdtEndDate Then
                                shouldInclude = False
                            End If
                        End If
                    End If
                End If

                If shouldInclude Then
                    result.Add(item)
                End If
            Next

            ' 如果沒有查詢條件，回傳所有資料
            If result.Count = 0 AndAlso
               String.IsNullOrEmpty(queryModel.DutyOrgID) AndAlso
               String.IsNullOrEmpty(queryModel.HourType) AndAlso
               String.IsNullOrEmpty(queryModel.BorrowerID) AndAlso
               String.IsNullOrEmpty(queryModel.ResID) AndAlso
               String.IsNullOrEmpty(queryModel.SubClassEx) AndAlso
               String.IsNullOrEmpty(queryModel.Week) AndAlso
               String.IsNullOrEmpty(queryModel.DayOfWeek) AndAlso
               String.IsNullOrEmpty(queryModel.StartDate) AndAlso
               String.IsNullOrEmpty(queryModel.StopDate) Then

                For Each item In MockDataStore
                    result.Add(item)
                Next
            End If

            Return result
        End Function
        ' 新增或更新 MockDataStore 資料的函式

        Public Function MockDataStore_Add(ppobj As YXA20Model) As ActionResult
            ' 先查找是否已存在相同 objid 的資料
            Dim existing = MockDataStore.FirstOrDefault(Function(x) x.objid = ppobj.objid)

            If existing IsNot Nothing Then
                ' 已存在則先移除再新增（模擬更新）
                MockDataStore.Remove(existing)
            Else
                ' 若 objid 為空或 -1 則自動給新編號
                If String.IsNullOrEmpty(ppobj.objid) OrElse ppobj.objid = "-1" Then
                    ppobj.objid = (MockDataStore.Count + 1).ToString()
                End If
            End If

            MockDataStore.Add(ppobj)

            Dim result As New myResult(Of YXA20Model) With {
                .OK = True,
                .MSG = "儲存成功",
                .obj = ppobj
            }
            Return Json(result, JsonRequestBehavior.AllowGet)
        End Function
        ' 根據 objid 刪除 MockDataStore 中的資料
        Public Function MockDataStore_Delete(objid As String) As ActionResult
            ' 依據 objid 移除所有符合的資料
            Dim removedCount = MockDataStore.RemoveAll(Function(x) x.objid = objid)
            Dim result As New myResult(Of String) With {
                .OK = removedCount > 0,
                .MSG = If(removedCount > 0, "刪除成功", "找不到資料"),
                .obj = objid
            }
            Return Json(result, JsonRequestBehavior.AllowGet)
        End Function



        '************Mock YXA20QModel**************
        ' 產生 Mock 測試資料(YXA20QModel)
        ' 全域記憶體模擬資料庫（以 objid 當作唯一識別）
        Public Shared Property MockYXA20QDataStore As New List(Of YXA20QModel) From {
            New YXA20QModel With {
                .objid = "001",
                .SemiYear = "112",
                .Semistry = "1",
                .DutyOrgID = "貴重儀器中心",
                .SubClassEx = "電子顯微鏡",
                .Core = "蛋白質體學核心",
                .ResID = "9321-25",
                .Location = "R9212-1",
                .BuyYear = "96",
                .ResName = "冷凍式超高速離心機",
                .ResNameEn = "Sorvall",
                .Contains = "5",
                .HourType = "A",
                .Week = "",
                .DayOfWeek = "1,2,3,4,5",
                .StartDate = "112/09/01",
                .EndDate = "112/12/31",
                .StartTime = "08:00",
                .EndTime = "17:00",
                .BorrowerID = "0",
                .IsRent = "Y",
                .ChargeItem = "儀器使用費",
                .TimeSlot = "日間",
                .BillingUnit = "小時",
                .Amount = "2000",
                .OtherFee = "100",
                .Deposit = "1000",
                .OverTime = "3",
                .OverTimeUnit = "小時",
                .Note = "需要培訓證書"
            },
            New YXA20QModel With {
                .objid = "002",
                .SemiYear = "112",
                .Semistry = "1",
                .DutyOrgID = "貴重儀器中心",
                .SubClassEx = "X光分析儀",
                .Core = "蛋白質體學核心",
                .ResID = "9321-56",
                .Location = "R9212-1",
                .BuyYear = "99",
                .ResName = "液相層析串聯質譜儀",
                .ResNameEn = "LC-MS",
                .Contains = "3",
                .HourType = "D",
                .Week = "1,3,5,7,9",
                .DayOfWeek = "1,2,3,4,5",
                .StartDate = "112/09/01",
                .EndDate = "112/12/31",
                .StartTime = "09:00",
                .EndTime = "16:00",
                .BorrowerID = "2",
                .IsRent = "Y",
                .ChargeItem = "儀器使用費",
                .TimeSlot = "夜間",
                .BillingUnit = "小時",
                .Amount = "3000",
                .OtherFee = "300",
                .Deposit = "3000",
                .OverTime = "3",
                .OverTimeUnit = "分鐘",
                .Note = "需要培訓證書，超時加收"
            },
            New YXA20QModel With {
                .objid = "003",
                .SemiYear = "112",
                .Semistry = "1",
                .DutyOrgID = "其他單位",
                .SubClassEx = "核磁共振",
                .Core = "蛋白質體學核心",
                .ResID = "9321-57",
                .Location = "R9212-1",
                .BuyYear = "101",
                .ResName = "高效液相層析串聯質譜儀",
                .ResNameEn = "nVloc",
                .Contains = "2",
                .HourType = "E",
                .Week = "1,3",
                .DayOfWeek = "1,3",
                .StartDate = "112/10/15",
                .EndDate = "112/10/15",
                .StartTime = "10:00",
                .EndTime = "15:00",
                .BorrowerID = "3",
                .IsRent = "N",
                .ChargeItem = "儀器使用費",
                .TimeSlot = "日間",
                .BillingUnit = "小時",
                .Amount = "4000",
                .OtherFee = "400",
                .Deposit = "4000",
                .OverTime = "3",
                .OverTimeUnit = "小時",
                .Note = "需要技術證書"
            },
            New YXA20QModel With {
                .objid = "004",
                .SemiYear = "112",
                .Semistry = "1",
                .DutyOrgID = "貴重儀器中心",
                .SubClassEx = "蛋白質分析",
                .Core = "蛋白質體學核心",
                .ResID = "9321-58",
                .Location = "R9212-1",
                .BuyYear = "104",
                .ResName = "高效液相層析串聯質譜儀",
                .ResNameEn = "LC-MS/MS Triple Q 8045",
                .Contains = "4",
                .HourType = "B",
                .Week = "1,3,5,7",
                .DayOfWeek = "1,3,5",
                .StartDate = "112/09/04",
                .EndDate = "112/12/29",
                .StartTime = "10:00",
                .EndTime = "16:00",
                .BorrowerID = "2",
                .IsRent = "Y",
                .ChargeItem = "儀器使用費",
                .TimeSlot = "日間",
                .BillingUnit = "小時",
                .Amount = "5000",
                .OtherFee = "500",
                .Deposit = "5000",
                .OverTime = "3",
                .OverTimeUnit = "小時",
                .Note = "不需要培訓證書"
            },
            New YXA20QModel With {
                .objid = "005",
                .SemiYear = "112",
                .Semistry = "1",
                .DutyOrgID = "貴重儀器中心",
                .SubClassEx = "基因分析",
                .Core = "蛋白質體學核心",
                .ResID = "9321-59",
                .Location = "R9212-1",
                .BuyYear = "109",
                .ResName = "高效液相層析串聯質譜儀",
                .ResNameEn = "LC-MS/MS Desktop TQ V1",
                .Contains = "6",
                .HourType = "C",
                .Week = "2,4,6,8",
                .DayOfWeek = "2,4,6",
                .StartDate = "112/09/05",
                .EndDate = "112/12/28",
                .StartTime = "09:00",
                .EndTime = "17:00",
                .BorrowerID = "3",
                .IsRent = "Y",
                .ChargeItem = "儀器使用費",
                .TimeSlot = "日間",
                .BillingUnit = "小時",
                .Amount = "6000",
                .OtherFee = "600",
                .Deposit = "6000",
                .OverTime = "3",
                .OverTimeUnit = "小時",
                .Note = "不需要培訓證書"
            }
        }

        '根據查詢條件篩選 MockYXA20QDataStore，回傳符合條件的 YXA20Q 清單
        ' <param name="queryModel">查詢條件</param>
        ' <returns>符合條件的 YXA20Q 清單</returns>
        Public Shared Function GenerateMockYXA20QData(queryModel As YXA20QModel) As List(Of YXA20QModel)
            Dim result As New List(Of YXA20QModel)

            ' 如果指定了 objid，直接返回匹配的單一項目
            If Not String.IsNullOrEmpty(queryModel.objid) Then
                Dim matchedItem = MockYXA20QDataStore.FirstOrDefault(Function(x) x.objid.Trim() = queryModel.objid.Trim())
                If matchedItem IsNot Nothing Then
                    result.Add(matchedItem)
                End If
                Return result
            End If

            For Each item In MockYXA20QDataStore
                Dim shouldInclude As Boolean = True

                ' 過濾 DutyOrgID、Core、ResID、Location、ResName
                If Not String.IsNullOrEmpty(queryModel.DutyOrgID) AndAlso
                   queryModel.DutyOrgID <> item.DutyOrgID Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(queryModel.Core) AndAlso
                   queryModel.Core <> item.Core Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(queryModel.ResID) AndAlso
                   queryModel.ResID <> item.ResID Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(queryModel.Location) AndAlso
                   queryModel.Location <> item.Location Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(queryModel.ResName) AndAlso
                   queryModel.ResName <> item.ResName Then
                    shouldInclude = False
                End If

                If shouldInclude Then
                    result.Add(item)
                End If

            Next

            ' 如果五個查詢條件都沒填，才回傳所有資料
            If result.Count = 0 AndAlso
               String.IsNullOrEmpty(queryModel.DutyOrgID) AndAlso
               String.IsNullOrEmpty(queryModel.Core) AndAlso
               String.IsNullOrEmpty(queryModel.ResID) AndAlso
               String.IsNullOrEmpty(queryModel.Location) AndAlso
               String.IsNullOrEmpty(queryModel.ResName) Then

                For Each item In MockYXA20QDataStore
                    result.Add(item)
                Next
            End If

            Return result
        End Function

        ' 新增或更新 MockYXA20QDataStore 資料的函式
        Public Function MockYXA20QDataStore_Add(ppobj As YXA20QModel) As ActionResult
            ' 先查找是否已存在相同儀器編號的資料（以儀器編號作為唯一識別）
            Dim existing = MockYXA20QDataStore.FirstOrDefault(Function(x) x.ResID = ppobj.ResID)

            If existing IsNot Nothing Then
                ' 已存在則先移除再新增（模擬更新）
                MockYXA20QDataStore.Remove(existing)
            End If

            MockYXA20QDataStore.Add(ppobj)

            Dim result As New myResult(Of YXA20QModel) With {
                .OK = True,
                .MSG = "儲存成功",
                .obj = ppobj
            }
            Return Json(result, JsonRequestBehavior.AllowGet)
        End Function


        ' 僅依據 ResID 刪除 MockYXA20QDataStore 中的資料
        Public Function MockYXA20QDataStore_Delete(resID As String) As ActionResult
            ' 依據 ResID 移除所有符合的資料
            Dim removedCount = MockYXA20QDataStore.RemoveAll(Function(x) x.ResID = resID)
            Dim result As New myResult(Of String) With {
                .OK = removedCount > 0,
                .MSG = If(removedCount > 0, "刪除成功", "找不到資料"),
                .obj = resID
            }
            Return Json(result, JsonRequestBehavior.AllowGet)
        End Function







        '************Mock YIC20_03ModelModel**************
        ' 建立 YIC20_03Model ，場地開放假資料使用的 Mock 資料，參考 controller 中貴儀中心的資料內容
        '場地開放假資料使用MockDataStore(YXA20Model)的資料
        Public Shared Property MockYIC20_03Model As New List(Of YIC20_03Model) From {
            New YIC20_03Model With {
                .objid = "001",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0301",
                .Core = "蛋白質體學核心",
                .ResID = "9321-25",
                .Location = "R921-1",
                .BuyYear = "101",
                .ResName = "多功能掃描電子顯微鏡",
                .ResNameEn = "Sputtercoater",
                .StartDate = "2024/07/01",
                .StartTime = "09:00",
                .StopDate = "2024/07/01",
                .StopTime = "12:00",
                .OPDTStr = "09:00~12:00",
                .BorrowerID = "S001",
                .Borrower = "學生",
                .BorrowerName = "王小明",
                .Phone = "0912345678",
                .Descript = "掃描電子顯微鏡分析",
                .Hour = "YYYYYYYYYYYYYYXX",
                .Status = "20",
                .StatusText = "待審核",
                .Reason = "",
                .OpDTobjid = "R001",
                .OpDTHour = "114/07/22 09:00 ~ 114/07/22 12:00"
            },
            New YIC20_03Model With {
                .objid = "002",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0301",
                .Core = "基因體學核心",
                .ResID = "9317-77",
                .Location = "R9377",
                .BuyYear = "104",
                .ResName = "基因分析系統",
                .ResNameEn = "Sputtercoater",
                .StartDate = "2025/07/01",
                .StartTime = "09:00",
                .StopDate = "2025/07/01",
                .StopTime = "12:00",
                .OPDTStr = "09:00~12:00",
                .BorrowerID = "S002",
                .Borrower = "學生",
                .BorrowerName = "李大明",
                .Phone = "0912345679",
                .Descript = "基因分析系統分析",
                .Hour = "YYXX",
                .Status = "20",
                .StatusText = "待審核",
                .Reason = "",
                .OpDTobjid = "R002",
                .OpDTHour = "114/07/22 09:00 ~ 114/07/22 12:00"
            },
            New YIC20_03Model With {
                .objid = "003",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0302",
                .Core = "細胞分析核心",
                .ResID = "9319-15",
                .Location = "R9319",
                .BuyYear = "104",
                .ResName = "細胞分析儀",
                .ResNameEn = "Sputtercoater",
                .StartDate = "2025/07/01",
                .StartTime = "09:00",
                .StopDate = "2025/07/01",
                .StopTime = "12:00",
                .OPDTStr = "09:00~12:00",
                .BorrowerID = "S001",
                .Borrower = "學生",
                .BorrowerName = "王小明",
                .Phone = "0912345678",
                .Descript = "細胞分析儀分析",
                .Hour = "YYYXX",
                .Status = "0",
                .StatusText = "待審核",
                .Reason = "",
                .OpDTobjid = "R003",
                .OpDTHour = "114/07/22 09:00 ~ 114/07/22 12:00"
            }
        }

        ' 依據 objid、Core、ResID 篩選 MockYIC20_03Model，回傳符合條件的表格欄位資料
        ' <returns>List(Of YIC20_03_Model) 包含所有表格欄位</returns>
        Public Function GetMockYIC20_03Model(objid As String, opdtobjid As String) As List(Of YIC20_03Model)
            Dim result As New List(Of YIC20_03Model)
            For Each item In MockYIC20_03Model
                Dim shouldInclude As Boolean = True


                If Not String.IsNullOrEmpty(objid) AndAlso
                   objid <> item.objid Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(opdtobjid) AndAlso
                   opdtobjid <> item.OpDTobjid Then
                    shouldInclude = False
                End If

                If shouldInclude Then
                    result.Add(item)
                End If

            Next

            Return result
        End Function

        'GetMockYIC20_03Model_First ，只回傳第一筆資料
        Public Function GetMockYIC20_03Model_First(objid As String) As YIC20_03Model
            If Not String.IsNullOrEmpty(objid) Then
                Dim matchedItem = MockYIC20_03Model.FirstOrDefault(Function(x) x.objid.Trim() = objid.Trim())
                If matchedItem IsNot Nothing Then
                    Return matchedItem
                End If
            End If
            Return Nothing
        End Function






        ' 建立 YIC20_03ResQ 的 Mock 資料，參考 MockYIC20_03ResModel 的 mockDATA
        Public Shared Property MockYIC20_03ResQ As New List(Of YIC20_03ResQ) From {
            New YIC20_03ResQ With {
                .SemiYear = "112",
                .Semistry = "1",
                .SubClassEx = "B0301",
                .Core = "蛋白質體學核心",
                .ResID = "9321-25",
                .Location = "R9321",
                .BuyYear = "103",
                .ResName = "質譜儀",
                .ResNameEn = "Mass Spectrometer",
                .StartDate = "2025/07/01",
                .EndDate = "2025/07/01",
                .StartTime = "09:00",
                .EndTime = "12:00",
                .BorrowerID = "2"
            },
            New YIC20_03ResQ With {
                .SemiYear = "112",
                .Semistry = "1",
                .SubClassEx = "B0302",
                .Core = "基因體學核心",
                .ResID = "9317-77",
                .Location = "R9317",
                .BuyYear = "104",
                .ResName = "基因分析系統",
                .ResNameEn = "Gene Analyzer",
                .StartDate = "2025/07/02",
                .EndDate = "2025/07/02",
                .StartTime = "13:00",
                .EndTime = "16:00",
                .BorrowerID = "3"
            },
            New YIC20_03ResQ With {
                .SemiYear = "112",
                .Semistry = "1",
                .SubClassEx = "B0303",
                .Core = "細胞分析核心",
                .ResID = "9319-15",
                .Location = "R9319",
                .BuyYear = "104",
                .ResName = "細胞分析儀",
                .ResNameEn = "Sputtercoater",
                .StartDate = "2025/07/01",
                .EndDate = "2025/07/01",
                .StartTime = "09:00",
                .EndTime = "12:00",
                .BorrowerID = "2"
            }
        }










        ' 建立 YIC20_03ResModel 的 Mock 資料，三組
        Public Shared Property MockYIC20_03ResModel As New List(Of YIC20_03ResModel) From {
            New YIC20_03ResModel With {
                .objid = "R001",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0301",
                .Core = "蛋白質體學核心",
                .ResID = "9321-25",
                .Location = "R9321",
                .BuyYear = "103",
                .ResName = "質譜儀",
                .ResNameEn = "Mass Spectrometer",
                .HourType = "1",
                .HourTypeStr = "單週",
                .Week = "1",
                .DayOfWeek = "一",
                .StartDate = "2025/07/01",
                .StartTime = "09:00",
                .StopDate = "2025/07/01",
                .StopTime = "12:00",
                .OPDTStr = "09:00~12:00",
                .BorrowerID = "2",
                .Borrower = "學生",
                .Hour = "YYXX",
                .Contains = "20"
            },
            New YIC20_03ResModel With {
                .objid = "R002",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0302",
                .Core = "基因體學核心",
                .ResID = "9317-77",
                .Location = "R9317",
                .BuyYear = "104",
                .ResName = "基因分析系統",
                .ResNameEn = "Gene Analyzer",
                .HourType = "2",
                .HourTypeStr = "雙週",
                .Week = "2",
                .DayOfWeek = "二",
                .StartDate = "2025/07/02",
                .StartTime = "13:00",
                .StopDate = "2025/07/02",
                .StopTime = "16:00",
                .OPDTStr = "13:00~16:00",
                .BorrowerID = "3",
                .Borrower = "教職員",
                .Hour = "YYXY",
                .Contains = "15"
            },
            New YIC20_03ResModel With {
                .objid = "R003",
                .DutyOrgID = "YIC20",
                .DutyOrgName = "貴重儀器中心",
                .SubClassEx = "B0303",
                .Core = "細胞分析核心",
                .ResID = "9319-15",
                .Location = "R9319",
                .BuyYear = "105",
                .ResName = "細胞分析儀",
                .ResNameEn = "Cell Analyzer",
                .HourType = "3",
                .HourTypeStr = "全週",
                .Week = "3",
                .DayOfWeek = "三",
                .StartDate = "2025/07/03",
                .StartTime = "08:00",
                .StopDate = "2025/07/03",
                .StopTime = "11:00",
                .OPDTStr = "08:00~11:00",
                .BorrowerID = "5",
                .Borrower = "校外單位",
                .Hour = "YXYX",
                .Contains = "10"
            }
        }


        '依據 core、resID、resName 篩選 MockYIC20_03ResModel，回傳符合條件的表格欄位資料
        Public Function GetMockYIC20_03ResModel(core As String, resID As String, resName As String) As List(Of YIC20_03ResModel)
            Dim result As New List(Of YIC20_03ResModel)
            For Each item In MockYIC20_03ResModel
                Dim shouldInclude As Boolean = True

                If Not String.IsNullOrEmpty(core) AndAlso core <> item.Core Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(resID) AndAlso resID <> item.ResID Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(resName) AndAlso resName <> item.ResName Then
                    shouldInclude = False
                End If

                If shouldInclude Then
                    result.Add(item)
                End If
            Next
            Return result
        End Function

















        'YIC20_03_Model申請單的假資料建置       
        ' 建立 YIC20_03_Model 的 Mock 資料，參考相片及 Public Class YIC20_03_Model，建立五組 mock data
        Public Shared Property MockYIC20_03_APL As New List(Of YIC20_03_Model) From {
            New YIC20_03_Model With {
                .objid = "1",
                .ApplyNo = "APL001",
                .DutyOrgName = "貴重儀器中心",
                .DutyOrgID = "YIC20",
                .SubClassEx = "9F",
                .Core = "蛋白質體學核心",
                .ResID = "9321-25",
                .Location = "R921-1",
                .BuyYear = "101",
                .ResName = "多功能掃描電子顯微鏡",
                .ResNameEn = "Sputtercoater",
                .ApplyTime = "2024/06/01 10:00",
                .Status = "待審核",
                .ApplicantName = "王小明",
                .ApplicantPhone = "0912345678",
                .EstimatedAmount = "5000",
                .Description = "掃描電子顯微鏡分析",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                    New ApplyTimeSlot With {.ApplyNo = "APL001", .DateValue = "2025-07-29", .TimeStart = "09:00", .TimeEnd = "09:30", .Status = "V"},
                    New ApplyTimeSlot With {.ApplyNo = "APL001", .DateValue = "2025-07-29", .TimeStart = "09:30", .TimeEnd = "10:00", .Status = "V"}
                }
            },
            New YIC20_03_Model With {
                .objid = "2",
                .ApplyNo = "APL002",
                .DutyOrgName = "貴重儀器中心",
                .DutyOrgID = "YIC20",
                .SubClassEx = "9F",
                .Core = "基因體學核心",
                .ResID = "9317-77",
                .Location = "R9377",
                .BuyYear = "101",
                .ResName = "基因分析系統",
                .ResNameEn = "Sputtercoater",
                .ApplyTime = "2024/08/01 10:00",
                .Status = "待審核",
                .ApplicantName = "李小應",
                .ApplicantPhone = "091234999",
                .EstimatedAmount = "1000",
                .Description = "基因分析系統分析",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                    New ApplyTimeSlot With {.ApplyNo = "APL002", .DateValue = "2025-07-29", .TimeStart = "09:00", .TimeEnd = "09:30", .Status = "V"},
                    New ApplyTimeSlot With {.ApplyNo = "APL002", .DateValue = "2025-07-29", .TimeStart = "09:30", .TimeEnd = "10:00", .Status = "V"}
                }
            },
            New YIC20_03_Model With {
                .objid = "3",
                .ApplyNo = "APL003",
                .DutyOrgName = "貴重儀器中心",
                .DutyOrgID = "YIC20",
                .SubClassEx = "9F",
                .Core = "細胞分析核心",
                .ResID = "9319-15",
                .Location = "R9319",
                .BuyYear = "101",
                .ResName = "細胞分析儀",
                .ResNameEn = "Sputtercoater",
                .ApplyTime = "2024/08/05 10:00",
                .Status = "待審核",
                .ApplicantName = "張三",
                .ApplicantPhone = "091234999",
                .EstimatedAmount = "3000",
                .Description = "細胞分析儀分析",
                .Deposit = "1000",
                .SelectedTimes = New List(Of ApplyTimeSlot) From {
                    New ApplyTimeSlot With {.ApplyNo = "APL003", .DateValue = "2025-07-29", .TimeStart = "09:00", .TimeEnd = "09:30", .Status = "V"},
                    New ApplyTimeSlot With {.ApplyNo = "APL003", .DateValue = "2025-07-29", .TimeStart = "09:30", .TimeEnd = "10:00", .Status = "V"}
                }
            }
        }


        '<summary>
        '依據 objid、ApplyNo、Core、ResID 篩選 MockYIC20_03_APL，回傳符合條件的 11 個表格欄位資料
        '<returns> List(Of YIC20_03_Model) 包含 11 個表格欄位</returns>
        Public Function GetMockYIC20_03_APLData(core As String, resID As String) As List(Of YIC20_03_Model)
            Dim result As New List(Of YIC20_03_Model)

            For Each item In MockYIC20_03_APL
                Dim shouldInclude As Boolean = True


                If Not String.IsNullOrEmpty(core) AndAlso
                   core <> item.Core Then
                    shouldInclude = False
                End If

                If Not String.IsNullOrEmpty(resID) AndAlso
                   resID <> item.ResID Then
                    shouldInclude = False
                End If

                If shouldInclude Then
                    result.Add(item)
                End If

            Next

            Return result
        End Function
        'GetMockYXA20_03_APLData_First ，只回傳第一筆資料
        Public Function GetMockYIC20_03_APLData_First(objid As String) As YIC20_03_Model
            If Not String.IsNullOrEmpty(objid) Then
                Dim matchedItem = MockYIC20_03_APL.FirstOrDefault(Function(x) x.objid.Trim() = objid.Trim())
                If matchedItem IsNot Nothing Then
                    Return matchedItem
                End If
            End If
            Return Nothing



        End Function













        '************Mock YXA20_03QModelModel**************


#End Region


    End Class
End Namespace